<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>智慧医疗患者管理系统 - 注册</title>
    <meta name="description" content="智慧医疗患者管理系统注册页面">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        .register-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
        }
        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .register-header h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .register-header p {
            color: #666;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-control {
            height: 45px;
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 0 15px;
            font-size: 14px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-register {
            width: 100%;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 20px;
        }
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ddd;
        }
        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 15px;
            color: #666;
            font-size: 14px;
        }
        .login-link {
            text-align: center;
        }
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .login-link a:hover {
            text-decoration: underline;
        }
        .alert {
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .user-type-selection {
            margin-bottom: 20px;
        }
        .user-type-card {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .user-type-card:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        .user-type-card.selected {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        .user-type-card input[type="radio"] {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="register-card">
                        <div class="register-header">
                            <h2>用户注册</h2>
                            <p>创建您的医疗健康管理账户</p>
                        </div>
                        
                        {% if error %}
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle"></i> {{ error }}
                        </div>
                        {% endif %}
                        
                        <form method="POST" action="{{ url_for('register') }}">
                            <div class="user-type-selection">
                                <label class="form-label">选择用户类型：</label>
                                <div class="user-type-card" onclick="selectUserType('patient')">
                                    <input type="radio" name="user_type" value="patient" id="patient" checked>
                                    <label for="patient">
                                        <strong><i class="fa fa-user text-primary"></i> 患者用户</strong><br>
                                        <small class="text-muted">查看个人健康档案，预约挂号，查看报告</small>
                                    </label>
                                </div>
                                <div class="user-type-card" onclick="selectUserType('admin')">
                                    <input type="radio" name="user_type" value="admin" id="admin">
                                    <label for="admin">
                                        <strong><i class="fa fa-user-md text-success"></i> 医护人员</strong><br>
                                        <small class="text-muted">管理患者信息，查看统计数据，系统管理</small>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="username">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="请输入用户名" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">邮箱地址</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="请输入邮箱地址" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">密码</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="请输入密码" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password">确认密码</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       placeholder="请再次输入密码" required>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="agree" required>
                                    <label class="form-check-label" for="agree">
                                        我同意 <a href="#" class="text-primary">用户协议</a> 和 <a href="#" class="text-primary">隐私政策</a>
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-register">
                                <i class="fa fa-user-plus"></i> 立即注册
                            </button>
                        </form>
                        
                        <div class="divider">
                            <span>或者</span>
                        </div>
                        
                        <div class="login-link">
                            <p>已有账户？ <a href="{{ url_for('login_page') }}">立即登录</a></p>
                        </div>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fa fa-info-circle"></i> 
                                演示系统：填写任意信息即可注册成功
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        function selectUserType(type) {
            $('.user-type-card').removeClass('selected');
            $('input[name="user_type"][value="' + type + '"]').prop('checked', true);
            $('input[name="user_type"][value="' + type + '"]').closest('.user-type-card').addClass('selected');
        }
        
        // 初始化选中状态
        $(document).ready(function() {
            selectUserType('patient');
        });
        
        // 表单验证
        $('form').on('submit', function(e) {
            var username = $('#username').val().trim();
            var email = $('#email').val().trim();
            var password = $('#password').val();
            var confirmPassword = $('#confirm_password').val();
            var agree = $('#agree').is(':checked');
            
            if (!username || !email || !password || !confirmPassword) {
                e.preventDefault();
                alert('请填写完整信息！');
                return false;
            }
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('两次输入的密码不一致！');
                return false;
            }
            
            if (!agree) {
                e.preventDefault();
                alert('请同意用户协议和隐私政策！');
                return false;
            }
        });
    </script>
</body>
</html>
