
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Mobile first web app theme | first</title>
  <meta name="description" content="mobile first, app, web app, responsive, admin dashboard, flat, flat ui">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="../static/css/bootstrap.css">
  <link rel="stylesheet" href="../static/css/font-awesome.min.css">
  <link rel="stylesheet" href="../static/css/style.css">
  <!--[if lt IE 9]>
    <script src="../static/js/ie/respond.min.js"></script>
    <script src="../static/js/ie/html5.js"></script>
  <![endif]-->
</head>
<body>
  <!-- header -->
  <header id="header" class="navbar">
    <ul class="nav navbar-nav navbar-avatar pull-right">
      <li class="dropdown">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown">
          <span class="hidden-sm-only">Mika Sokeil</span>
          <span class="thumb-small avatar inline"><img src="../static/images/avatar.jpg" alt="Mika Sokeil" class="img-circle"></span>
          <b class="caret hidden-sm-only"></b>
        </a>
        <ul class="dropdown-menu">
          <li><a href="#">Settings</a></li>
          <li><a href="#">Profile</a></li>
          <li><a href="#"><span class="badge bg-danger pull-right">3</span>Notifications</a></li>
          <li class="divider"></li>
          <li><a href="docs.html">Help</a></li>
          <li><a href="signin.html">Logout</a></li>
        </ul>
      </li>
    </ul>
    <a class="navbar-brand" href="#">   </a>    #标题
    <button type="button" class="btn btn-link pull-left nav-toggle hidden-lg" data-toggle="class:show" data-target="#nav">
      <i class="icon-reorder icon-xlarge text-default"></i>
    </button>
   <ul class="nav navbar-nav hidden-sm">
      <li>
        <div class="m-t m-b-small" id="panel-notifications">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="icon-comment-alt icon-xlarge text-default"></i><b class="badge badge-notes bg-danger count-n">2</b></a>
          <section class="dropdown-menu m-l-small m-t-mini">
            <section class="panel panel-large arrow arrow-top">
              <header class="panel-heading bg-white"><span class="h5"><strong>你有 <span class="count-n">2</span> 条通知</strong></span></header>
              <div class="list-group list-group-flush m-t-n">
                <a href="#" class="media list-group-item">
                  <span class="pull-left thumb-small"><img src="../static/images/avatar.jpg" alt="John said" class="img-circle"></span>
                  <span class="media-body block m-b-none">
                    顺利毕业<br>
                    <small class="text-muted">23 June 22</small>
                  </span>
                </a>
                <a href="#" class="media list-group-item">
                  <span class="media-body block m-b-none">
                    首次使用Python医疗数据可视化系统<br>
                    <small class="text-muted">29 Mar 22</small>
                  </span>
                </a>
              </div>
              <footer class="panel-footer text-small">
                <a href="#" class="pull-right"><i class="icon-cog"></i></a>
                <a href="#">查看所有通知</a>
              </footer>
            </section>
          </section>
        </div>
      </li>
      <li><div class="m-t-small"><a class="btn btn-small btn-info" data-toggle="modal" href="#modal"><i class="icon-plus"></i> 添加患者</a></div></li>
      <li class="dropdown shift" data-toggle="shift:appendTo" data-target=".nav-primary .nav">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="icon-cog icon-xlarge visible-sm visible-sm-inline"></i>设置 <b class="caret hidden-sm-only"></b></a>
        <ul class="dropdown-menu">
          <li>
            <a href="#" data-toggle="class:navbar-fixed" data-target='body'>Navbar
              <span class="text-active">auto</span>
              <span class="text">fixed</span>
            </a>
          </li>
          <li class="visible-lg">
            <a href="#" data-toggle="class:nav-vertical" data-target="#nav">Nav
              <span class="text-active">vertical</span>
              <span class="text">horizontal</span>
            </a>
          </li>
          <li class="divider hidden-sm"></li>
          <li class="dropdown-header">颜色</li>
          <li>
            <a href="#" data-toggle="class:bg bg-black" data-target='.navbar'>Navbar
              <span class="text-active">white</span>
              <span class="text">inverse</span>
            </a>
          </li>
          <li>
            <a href="#" data-toggle="class:bg-light" data-target='#nav'>Nav
              <span class="text-active">inverse</span>
              <span class="text">light</span>
            </a>
          </li>
        </ul>
      </li>
    </ul>
    <form class="navbar-form pull-left shift" action="" data-toggle="shift:appendTo" data-target=".nav-primary">
      <i class="icon-search text-muted"></i>
      <input type="text" class="input-small form-control" placeholder="Search">
    </form>
  </header>
  <!-- / header -->
  <!-- nav -->
  <nav id="nav" class="nav-primary visible-lg nav-vertical">
    <ul class="nav" data-spy="affix" data-offset-top="50">
      <li><a href="{{url_for('index')}}"><i class="icon-dashboard icon-xlarge"></i>主页</a></li>

        <li class="dropdown-submenu  active">
        <a href="{{url_for('table')}}"><i class="icon-list icon-xlarge"></i>患者列表</a>
      </li>

      <li class="dropdown-submenu "><a href="{{url_for('chart')}}"><i class="icon-signal icon-xlarge"></i>可视化</a></li>
<li class="dropdown-submenu">
        <a href="{{ url_for('relations') }}"><i class="icon-link icon-xlarge"></i>疾病关联分析</a>
      </li>
    </ul>
  </nav>
  <!-- / nav -->
  <section id="content">
    <section class="main padder">
      <div class="clearfix">
        <h4>Python医疗数据可视化系统</h4>
      </div>
      <div class="row">
        <div class="col-lg-8">
          <section class="panel">
            <header class="panel-heading">
              Stats
            </header>
            <div class="btn-group" data-toggle="buttons">
              <label >
                <p>来诊人数时间分布图</p>
              </label>

            </div>
            <div class="line line-large pull-in"></div>
            <div class="sparkline" data-type="line" data-resize="true" data-height="200" data-width="100%" data-line-color="#bfea5f" data-fill-color="#f3fce3" data-highlight-line-color="#e1e5e9" data-spot-radius="5" data-composite-data="[160,230,250,300,320,330,280,260,250,280,250,260,250,255,330,345,300,210,200,200,170,180,250,250,200,200,280,270,310,250,280,175]" data-composite-line-color="#a3e2fe" data-composite-fill-color="#e3f6ff" data-data="[120,250,200,325,400,380,250,320,345,250,250,250,200,325,300,365,250,210,200,180,150,160,250,250,250,200,300,310,330,250,320,205]"></div>
            <ul class="list-inline text-muted axis"><li>12:00<br>am</li><li>2:00</li><li>4:00</li><li>6:00</li><li>8:00</li><li>10:00</li><li>12:00<br>pm</li><li>2:00</li><li>4:00</li><li>6:00</li><li>8:00</li><li>10:00</li></ul>
          </section>
        </div>
        <div class="col-lg-4">
          <section class="panel">
            <header class="panel-heading">
              今日新增患者
            </header>
            <div class="pull-in text-center" >
              <h4 id="data">{{new_add}}<small> 人</small></h4>
              <small class="text-muted block">更新于刚刚</small>
              <div class="inline">
                <div class="easypiechart" data-percent="75" data-line-width="16" data-loop="false" data-size="188">
                  <span class="h2" style="margin-left:10px;margin-top:10px;display:inline-block">{{total}}</span>%
                  <div class="easypie-text"><button class="btn btn-link m-t-n-small" data-toggle="class:pie"><i class="icon-play text text-muted"></i><i class="icon-pause text-active text-muted"></i></button></div>
                </div>
              </div>

              <div class="line"></div>
              <div><small>% of avarage rate of the visits</small></div>
            </div>
          </section>
        </div>
      </div>
      <div class="row">
        <div class="col-lg-4">
          <section class="panel">
            <header class="panel-heading">聚类饼图</header>
            <div class="text-center" id="myPie">
              <div  id="sparkline" class="sparkline inline" data-type="pie" data-height="175" data-slice-colors="['#233445','#3fcf7f','#ff5f5f','#f4c414','#13c4a5','#656565','#d8841c']">{[jjfb]},{[hhgx]},{[rjgx]},{[yyyd]},{[xryl]},{[gyl]},{[sy]}</div>
              <div class="text-mini">
                <i class="icon-circle text-info"></i> 就近方便型
                <i class="icon-circle text-success"></i> 合同关系型
                <i class="icon-circle text-danger"></i> 人际关系型
                <i class="icon-circle text-primary"></i> 舆论诱导型
                <i class="icon-circle text-warning"></i> 信任医疗型
                <i class="icon-circle text"></i> 高医疗消费型
                <i class="icon-circle text-default"></i> 随意就医型

              </div>
            </div>
          </section>
        </div>
        <div class="col-lg-4">
          <section class="panel">
            <header class="panel-heading">每日新增患者折线图</header>
            <div class="text-center">
              <div class="inline">
                <div   class="sparkline inline" data-type="bar" data-height="145" data-bar-width="20" data-bar-spacing="10" data-bar-color="#a3e2fe" data-composite-data="[10,8,12,13,22,15,18]" data-composite-line-color="#bfea5f" data-composite-fill-color="#f3fce3" data-highlight-line-color="#e1e5e9" data-spot-radius="3">5,15,12,18,20,17,13</div>
                <ul class="list-inline text-muted axis"><li>M<br>5%</li><li>T<br>15%</li><li>W<br>12%</li><li>T<br>18%</li><li>F<br>20%</li><li>S<br>17%</li><li>S<br>13%</li></ul>
              </div>
              <div class="line pull-in"></div>
              <div class="text-small">Check more data</div>
            </div>
          </section>
        </div>

      </div>
    </section>
  </section>
  <!-- footer -->
  <footer id="footer">
    <div class="text-center padder clearfix">
      <p>
        <small>&copy; 基于Python的医疗数据可视化系统  </small><br><br>
        <a href="#" class="btn btn-mini btn-circle btn-twitter"><i class="icon-twitter"></i></a>
        <a href="#" class="btn btn-mini btn-circle btn-facebook"><i class="icon-facebook"></i></a>
        <a href="#" class="btn btn-mini btn-circle btn-gplus"><i class="icon-google-plus"></i></a>
      </p>
    </div>
  </footer>
  <!-- / footer -->
	<script src="../static/js/jquery.min.js"></script>
  <!-- Bootstrap -->
  <script src="../static/js/bootstrap.js"></script>
  <!-- app -->
  <script src="../static/js/app.js"></script>
  <script src="../static/js/app.plugin.js"></script>
  <script src="../static/js/app.data.js"></script>
  <!-- Sparkline Chart -->
  <script src="../static/js/charts/sparkline/jquery.sparkline.min.js"></script>
  <script src="../static/js/ie/excanvas.js"></script>
  <!-- Easy Pie Chart -->
  <script src="../static/js/charts/easypiechart/jquery.easy-pie-chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script>
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
  <script>
    $(function(){
       var myPie=new Vue({
      delimiters: ['{[', ']}'],
      el:'#myPie',
      data:{
        gyl:'1',
        hhgx:'2',
        jjfb:'3',
        rjgx:'4',
        sy:'5',
        xryl:'6',
        yyyd:'7',
      },
      mounted(){
        this.getMypie();
      },
      methods:{
        getMypie:function(){
          var that=this;
          axios.get('/mypie').then(function (response){

            myres=response.data;
            that.gyl=myres.gyl;
            console.log("that.gyl:"+that.gyl)
            that.hhgx=myres.hhgx;
            that.jjfb=myres.jjfb;
            that.rjgx=myres.rjgx;
            that.sy=myres.sy;
            that.xryl=myres.xryl;
            that.yyyd=myres.yyyd;
            myvalues=[that.jjfb,that.hhgx,that.rjgx,that.yyyd,that.xryl,that.gyl,that.sy]
            $('#sparkline').sparkline(myvalues,{ type:'pie',height:'175' });
          }).catch(function (error){
            console.log((error))
          })
        },
        getdata:function (){
          console.log(this.gyl);
        }

      }

    })

    })
</script>




</body>
</html>