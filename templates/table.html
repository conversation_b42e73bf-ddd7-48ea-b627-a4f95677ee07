<!DOCTYPE html>
{% extends 'base.html' %}
<html lang="en">

<body>
  <!-- header -->

  <!-- / nav -->
  {% block content%}
  <section id="content">
    <section class="main padder">
      <div class="clearfix">
        <h4><i class="icon-table"></i>患者数据</h4>
      </div>
      <div class="row">

        <div class="col-lg-12" id="load">
          <section class="panel">
            <header class="panel-heading">
              患者数据
            </header>
            <div class="row text-small">

              <div class="col-lg-4">
                <div class="input-group">
                  <input type="text" class="input-small form-control" placeholder="请输入患者的姓名：" v-model="search_name">
                  <span class="input-group-btn" >
                    <button class="btn btn-small btn-white" type="button" @click="search()">Go!</button>
                  </span>
                </div>
              </div>
            </div>
            <div class="pull-out m-t-small" >
              <div id="modal" class="modal fade">
    <form class="m-b-none">
      <div class="modal-dialog pos-abt" style="margin-top:-235px; top:50%">
        <div class="modal-content" id="postform">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icon-remove"></i></button>
            <h4 class="modal-title" id="myModalLabel">添加患者信息</h4>
          </div>
          <div class="modal-body" >
            <div class="block">
              <label class="control-label">姓名</label>
              <input type="text" class="form-control" placeholder="患者姓名" name="name" v-model="name" disabled>
              <label class="control-label">患者症状</label>
              <input type="text" class="form-control" placeholder="患者症状" name="age" v-model="detail" disabled>
            </div>
            <div class="block">
              <label class="control-label">回复内容（医嘱和建议）</label>
              <textarea class="form-control" placeholder="输入患者的详细状况" rows="5" v-model="resp"></textarea>
            </div>
<!--            <div class="checkbox">-->
<!--              <label>-->
<!--                <input type="checkbox"> Share with all memebers of first-->
<!--              </label>-->
<!--            </div>-->
          </div>
          <div class="modal-footer" >

            <button type="button" class="btn btn-small btn-primary" data-dismiss="modal" @click="addrep()">提交</button>
          </div>
        </div><!-- /.modal-content -->
      </div>
    </form>
  </div>
              <table class="table table-striped b-t text-small" >

                <thead>
                  <tr>

                    <th class="th-sortable" data-toggle="class">姓名
                    </th>
                    <th>患者类型</th>
                    <th>病状</th>
                    <th>医嘱和建议</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item,index) in info" :key="index">
                    <td v-text="item.name">213</td>
                    <td v-text="item.behavior">4c</td>
                    <td v-text="item.detail">Jul 25, 2013</td>
                    <td v-text="item.doc_resp">巴拉巴拉</td>
                    <td>
                      <a href="#" class="active" data-toggle="class" v-show="item.doc_resp"><i class="icon-ok icon-large text-success text-active"></i>已回复</a>
                      <a href="#modal" data-toggle="modal"><i class="icon-edit icon-large  text" v-show="!item.doc_resp" @click="loadmodal(item.name,item.detail)">回复患者</i></a>|<a href="#" @click="deleteuser(item.name)"><i class="icon-remove"></i>删除患者</a>
                    </td>
                  </tr>


                </tbody>
              </table>
            </div>
            <footer class="panel-footer">
              <div class="row">



              </div>
            </footer>
          </section>
        </div>

      </div>
    </section>
  </section>
  {% endblock%}
  <!-- footer -->

  <!-- .modal -->


  <!-- / footer -->
  {% block js%}
	<script src="../static/js/jquery.min.js"></script>
  <!-- Bootstrap -->
  <script src="../static/js/bootstrap.js"></script>
  <!-- fuelux -->
  <script src="../static/js/fuelux/fuelux.js"></script>
  <script src="../static/js/underscore-min.js"></script>
  <!-- datatables -->
  <script src="../static/js/datatables/jquery.dataTables.min.js"></script>
  <!-- Sparkline Chart -->
  <script src="../static/js/charts/sparkline/jquery.sparkline.min.js"></script>
  <!-- Easy Pie Chart -->
  <script src="../static/js/charts/easypiechart/jquery.easy-pie-chart.js"></script>

  <!-- app -->
  <script src="../static/js/app.js"></script>
  <script src="../static/js/app.plugin.js"></script>
  <script src="../static/js/app.data.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script>
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script>
var app=new Vue({
  delimiters: ['{[', ']}'],
  el:'#load',
  data:{
    info:[],
    name:'www',
    detail:'hhh',
    resp:'',
    search_name:'',
  },
  mounted(){
    this.loadPatient()
  },
  methods:{
    loadPatient:function (){
      var that=this;
      axios.get('/show').then(function (response){
        that.info=response.data.data;

      }).catch(function (error) {
        console.log(error);
      })
    },
    search:function () {
      var that=this;
      axios.get('/search_user?name='+this.search_name).then(function (response) {
        that.info=response.data;
        console.log(that.info)
      }).catch(function (error) {
        console.log('failed')
      })
    },
    loadmodal:function (name,detail){
      console.log(name)
      this.name=name;
      this.detail=detail;
    },
    deleteuser:function (name){
      console.log(name);
      alert("成功删除患者："+name)
      axios.get('/removeUser?name='+name).then(function (response) {
        console.log('success');
        location.reload();
      }).catch(function (error) {
        console.log('failed');
      })
    },
    addrep:function (){
      data={
        name:this.name,
        detail:this.detail,
        resp:this.resp
      }
      axios.post('/addrep',data).then(function (response){
        alert("回复成功！")
        location.reload()
        console.log('success')
      }).catch(function (error){
        console.log(error)
      })
    }
  }
})
</script>
{% endblock%}
</body>
</html>