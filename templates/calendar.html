<!DOCTYPE html>
{% extends 'base.html' %}
<html lang="en">
<body>
  <!-- / nav -->
  {% block content %}
  <section id="content" class="content-sidebar bg-white">
    <!-- .sidebar -->
    <aside class="sidebar bg-lighter padder clearfix">
      <h5>可拖动的事件</h5>
      <div class="line"></div>
      <div id="myEvents" class="pillbox clearfix m-b no-border no-padder">
        <ul>
          <li class="label bg-inverse">朋友聚会</li>
          <li class="label bg-success">整理工作</li>
          <li class="label bg-warning">项目汇报</li>
          <li class="label bg-danger">日程管理</li>
          <li class="label bg-info">休闲娱乐</li>

          <input type="text" placeholder="添加一个事件">
        </ul>
      </div>
      <div class="line"></div>
      <div class="checkbox">
        <label class="checkbox-custom"><input type='checkbox' id='drop-remove' /><i class="icon-unchecked"></i> 结束后移除</label>
      </div>
    </aside>
    <!-- /.sidebar -->
    <!-- .main -->
    <section class="main">
      <div class="calendar" id="calendar">

      </div>
    </section>
    <!-- /.main -->
  </section>
  {% endblock %}
  {% block js %}
	<script src="../static/js/jquery.min.js"></script>
  <!-- Bootstrap -->
  <script src="../static/js/bootstrap.js"></script>

  <script src="../static/js/fuelux/fuelux.js"></script>
  <!-- fullcalendar -->
  <script src="../static/js/fullcalendar/jquery-ui-1.10.2.custom.min.js"></script>
  <script src="../static/js/fullcalendar/jquery.ui.touch-punch.min.js"></script>
  <script src="../static/js/fullcalendar/fullcalendar.min.js"></script>
  <!-- Sparkline Chart -->
  <script src="../static/js/charts/sparkline/jquery.sparkline.min.js"></script>
  <!-- Easy Pie Chart -->
  <script src="../static/js/charts/easypiechart/jquery.easy-pie-chart.js"></script>

  <!-- app -->
  <script src="../static/js/app.js"></script>
  <script src="../static/js/app.plugin.js"></script>
  <script src="../static/js/app.data.js"></script>
{% endblock %}
</body>
</html>