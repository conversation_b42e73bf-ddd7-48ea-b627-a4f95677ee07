<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>管理后台 - 智慧医疗</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-custom .navbar-brand, .navbar-custom .navbar-nav .nav-link {
            color: white !important;
        }
        .content-wrapper { padding: 30px 0; }
        .admin-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 20px;
        }
        .welcome-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
        }
        .quick-action-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
            border: 2px solid transparent;
        }
        .quick-action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        .action-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #667eea;
        }
        .action-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .action-desc {
            color: #666;
            font-size: 14px;
        }
        .frontend-highlight {
            border: 3px solid #28a745;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .frontend-highlight .action-icon {
            color: white;
        }
        .frontend-highlight .action-title,
        .frontend-highlight .action-desc {
            color: white;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-heartbeat"></i> 智慧医疗管理后台
            </a>
            <div class="navbar-nav ml-auto">
                <a class="nav-link" href="{{ url_for('user_dashboard') }}">
                    <i class="fa fa-user"></i> 前台用户中心
                </a>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fa fa-sign-out"></i> 退出登录
                </a>
            </div>
        </div>
    </nav>

    <div class="content-wrapper">
        <div class="container">
            <!-- 欢迎横幅 -->
            <div class="welcome-banner">
                <h1><i class="fa fa-user-md"></i> 欢迎使用智慧医疗管理系统</h1>
                <p class="lead">为医护人员提供全面的患者管理和数据分析工具</p>
                <div class="mt-4">
                    <button class="btn btn-light btn-lg mr-3" onclick="location.href='{{ url_for('user_dashboard') }}'">
                        <i class="fa fa-user"></i> 体验前台用户中心
                    </button>
                    <button class="btn btn-outline-light btn-lg" onclick="location.href='{{ url_for('admin_dashboard') }}'">
                        <i class="fa fa-cog"></i> 进入原版后台
                    </button>
                </div>
            </div>

            <!-- 统计数据 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">总患者数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">89</div>
                    <div class="stat-label">今日新增</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">456</div>
                    <div class="stat-label">待处理</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">系统健康度</div>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="row">
                <div class="col-12">
                    <h3><i class="fa fa-bolt"></i> 快捷操作</h3>
                    <p class="text-muted">选择您要执行的操作</p>
                </div>
            </div>

            <div class="row">
                <!-- 前台用户中心 - 突出显示 -->
                <div class="col-md-4">
                    <div class="quick-action-card frontend-highlight" onclick="location.href='{{ url_for('user_dashboard') }}'">
                        <div class="action-icon">
                            <i class="fa fa-user"></i>
                        </div>
                        <div class="action-title">前台用户中心</div>
                        <div class="action-desc">
                            体验患者视角的系统功能<br>
                            包含健康档案、报告查看等
                        </div>
                        <div class="mt-3">
                            <span class="badge badge-light">推荐体验</span>
                        </div>
                    </div>
                </div>

                <!-- 患者管理 -->
                <div class="col-md-4">
                    <div class="quick-action-card" onclick="location.href='{{ url_for('admin_dashboard') }}'">
                        <div class="action-icon">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="action-title">患者管理</div>
                        <div class="action-desc">
                            查看和管理患者信息<br>
                            添加、编辑患者档案
                        </div>
                    </div>
                </div>

                <!-- 数据可视化 -->
                <div class="col-md-4">
                    <div class="quick-action-card" onclick="location.href='{{ url_for('chart') }}'">
                        <div class="action-icon">
                            <i class="fa fa-bar-chart"></i>
                        </div>
                        <div class="action-title">数据可视化</div>
                        <div class="action-desc">
                            查看统计图表和分析报告<br>
                            患者数据趋势分析
                        </div>
                    </div>
                </div>

                <!-- 患者列表 -->
                <div class="col-md-4">
                    <div class="quick-action-card" onclick="location.href='{{ url_for('table') }}'">
                        <div class="action-icon">
                            <i class="fa fa-list"></i>
                        </div>
                        <div class="action-title">患者列表</div>
                        <div class="action-desc">
                            查看详细的患者列表<br>
                            搜索和筛选功能
                        </div>
                    </div>
                </div>

                <!-- 疾病关联分析 -->
                <div class="col-md-4">
                    <div class="quick-action-card" onclick="location.href='{{ url_for('relations') }}'">
                        <div class="action-icon">
                            <i class="fa fa-link"></i>
                        </div>
                        <div class="action-title">疾病关联分析</div>
                        <div class="action-desc">
                            分析疾病之间的关联性<br>
                            数据挖掘和模式识别
                        </div>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div class="col-md-4">
                    <div class="quick-action-card" onclick="alert('系统设置功能开发中...')">
                        <div class="action-icon">
                            <i class="fa fa-cog"></i>
                        </div>
                        <div class="action-title">系统设置</div>
                        <div class="action-desc">
                            系统配置和参数设置<br>
                            用户权限管理
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="row mt-4">
                <div class="col-md-8">
                    <div class="admin-card">
                        <h5><i class="fa fa-info-circle"></i> 系统说明</h5>
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <strong>双端体验：</strong> 本系统提供管理后台和用户前台两套界面，您可以切换体验不同角色的功能。
                            </div>
                            <div class="alert alert-success">
                                <strong>功能完整：</strong> 包含患者管理、数据可视化、健康档案、报告查看等完整功能模块。
                            </div>
                            <div class="alert alert-warning">
                                <strong>演示系统：</strong> 这是演示版本，所有数据均为模拟数据，仅供功能展示使用。
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="admin-card">
                        <h5><i class="fa fa-question-circle"></i> 快速帮助</h5>
                        <div class="mt-3">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fa fa-check text-success"></i> 
                                    点击"前台用户中心"体验患者功能
                                </li>
                                <li class="mb-2">
                                    <i class="fa fa-check text-success"></i> 
                                    使用"患者管理"查看后台功能
                                </li>
                                <li class="mb-2">
                                    <i class="fa fa-check text-success"></i> 
                                    "数据可视化"查看统计图表
                                </li>
                                <li class="mb-2">
                                    <i class="fa fa-check text-success"></i> 
                                    所有功能均可正常使用
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        // 添加一些交互效果
        $(document).ready(function() {
            // 卡片悬停效果
            $('.quick-action-card').hover(
                function() {
                    $(this).find('.action-icon').addClass('animated pulse');
                },
                function() {
                    $(this).find('.action-icon').removeClass('animated pulse');
                }
            );
            
            // 显示欢迎消息
            setTimeout(function() {
                console.log('智慧医疗管理系统已加载完成');
            }, 1000);
        });
    </script>
</body>
</html>
