
<!DOCTYPE html>
{% extends 'base.html' %}
<html lang="en">

<body>
  <!-- header -->
	<header id="header" class="navbar">
    <ul class="nav navbar-nav navbar-avatar pull-right">
      <li class="dropdown">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown">            
          <span class="hidden-sm-only">Admin</span>
          <span class="thumb-small avatar inline"><img src="../static/images/avatar.jpg" alt="Mika Sokeil" class="img-circle"></span>
          <b class="caret hidden-sm-only"></b>
        </a>
        <ul class="dropdown-menu">
<!--          <li><a href="#">Settings</a></li>-->
<!--          <li><a href="#">Profile</a></li>-->
<!--          <li><a href="#"><span class="badge bg-danger pull-right">3</span>Notifications</a></li>-->
<!--          <li class="divider"></li>-->
<!--          <li><a href="docs.html">Help</a></li>-->
          <li><a href="signin.html">退出</a></li>
        </ul>
      </li>
    </ul>
    <a class="navbar-brand" href="#">Python医疗数据可视化系统</a>
    <button type="button" class="btn btn-link pull-left nav-toggle hidden-lg" data-toggle="class:slide-nav slide-nav-left" data-target="body">
      <i class="icon-reorder icon-xlarge text-default"></i>
    </button>
    <ul class="nav navbar-nav hidden-sm">
      <li>
        <div class="m-t m-b-small" id="panel-notifications">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="icon-comment-alt icon-xlarge text-default"></i><b class="badge badge-notes bg-danger count-n">2</b></a>
          <section class="dropdown-menu m-l-small m-t-mini">
            <section class="panel panel-large arrow arrow-top">
              <header class="panel-heading bg-white"><span class="h5"><strong>你有 <span class="count-n">2</span> 条通知</strong></span></header>
              <div class="list-group list-group-flush m-t-n">
                <a href="#" class="media list-group-item">
                  <span class="pull-left thumb-small"><img src="../static/images/avatar.jpg" alt="John said" class="img-circle"></span>
                  <span class="media-body block m-b-none">
                    顺利毕业<br>
                    <small class="text-muted">23 June 22</small>
                  </span>
                </a>
                <a href="#" class="media list-group-item">
                  <span class="media-body block m-b-none">
                    首次使用Python医疗数据可视化系统<br>
                    <small class="text-muted">29 Mar 22</small>
                  </span>
                </a>
              </div>
              <footer class="panel-footer text-small">
                <a href="#" class="pull-right"><i class="icon-cog"></i></a>
                <a href="#">查看所有通知</a>
              </footer>
            </section>
          </section>
        </div>
      </li>
      <li><div class="m-t-small"><a class="btn btn-small btn-info" data-toggle="modal" href="#modal"><i class="icon-plus"></i> 添加患者</a></div></li>
      <li class="dropdown shift" data-toggle="shift:appendTo" data-target=".nav-primary .nav">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="icon-cog icon-xlarge visible-sm visible-sm-inline"></i>设置 <b class="caret hidden-sm-only"></b></a>
        <ul class="dropdown-menu">
          <li>
            <a href="#" data-toggle="class:navbar-fixed" data-target='body'>Navbar
              <span class="text-active">auto</span>
              <span class="text">fixed</span>
            </a>
          </li>
          <li class="visible-lg">
            <a href="#" data-toggle="class:nav-vertical" data-target="#nav">Nav 
              <span class="text-active">vertical</span>
              <span class="text">horizontal</span>
            </a>
          </li>
          <li class="divider hidden-sm"></li>
          <li class="dropdown-header">颜色</li>
          <li>
            <a href="#" data-toggle="class:bg bg-black" data-target='.navbar'>Navbar 
              <span class="text-active">white</span>
              <span class="text">inverse</span>
            </a>
          </li>
          <li>
            <a href="#" data-toggle="class:bg-light" data-target='#nav'>Nav 
              <span class="text-active">inverse</span>
              <span class="text">light</span>
            </a>
          </li>
        </ul>
      </li>
    </ul>
    <form class="navbar-form pull-left shift" action="" data-toggle="shift:appendTo" data-target=".nav-primary">
      <i class="icon-search text-muted"></i>
      <input type="text" class="input-small form-control" placeholder="Search">
    </form>
	</header>
  <!-- / header -->
  <!-- nav -->
  <nav id="nav" class="nav-primary visible-lg nav-vertical">
    <ul class="nav" data-spy="affix" data-offset-top="50">
      <li class="active"><a href="index.html"><i class="icon-dashboard icon-xlarge"></i>主页</a></li>

      <li class="dropdown-submenu">
        <a href="{{url_for('table')}}"><i class="icon-list icon-xlarge"></i>患者列表</a>
      </li>

      <li><a href="{{url_for('chart')}}"><i class="icon-signal icon-xlarge"></i>可视化</a></li>
  <li class="dropdown-submenu">
        <a href="{{ url_for('relations') }}"><i class="icon-link icon-xlarge"></i>疾病关联分析</a>
      </li>
      <li class="dropdown-submenu">
        <a href="{{ url_for('user_dashboard') }}" style="color: #28a745;"><i class="icon-user icon-xlarge"></i>进入前台用户中心</a>
      </li>
    </ul>
  </nav>
  <!-- / nav -->
  <section id="content">
    <section class="main padder">
      <div class="row">
        <div class="col-lg-12">
          <section class="toolbar clearfix m-t-large m-b">
            <a href="https://mail.qq.com/" class="btn btn-white btn-circle"><i class="icon-envelope-alt"></i>收件箱 <b class="badge bg-white">5</b></a>
            <a href="#" class="btn btn-primary btn-circle active"><i class="icon-lightbulb"></i>工程</a>
            <a href="#" class="btn btn-success btn-circle"><i class="icon-check"></i>任务</a>
            <a href="{{url_for('calendar')}}" class="btn btn-info btn-circle active"><i class="icon-time"></i>时间线<b class="badge bg-info"><i class="icon-plus"></i></b></a>
            <a href="#" class="btn btn-inverse btn-circle"><i class="icon-bar-chart"></i>状态</a>
            <a href="calendar.html" class="btn btn-warning btn-circle"><i class="icon-calendar-empty"></i>日程</a>
            <a href="#" class="btn btn-danger btn-circle"><i class="icon-group"></i>群组</a>
            <a href="#" class="btn btn-circle"><i class="icon-plus"></i>更多</a>
          </section>
        </div>
        <div class="col-lg-6">
          <div class="row">
            <!-- easypiechart -->
            <div class="col-6">              
              <section class="panel">
                <header class="panel-heading bg-white">
                  <div class="text-center h5">运行/状态 饼图</div>
                </header>
                <div class="pull-in text-center">
                  <div class="inline">
                    <div class="easypiechart" data-percent="55" data-loop="true">
                      <span class="h2" style="margin-left:10px;margin-top:10px;display:inline-block">55</span>%
                      <div class="easypie-text"><button class="btn btn-link m-t-n-small" data-toggle="class:pie"><i class="icon-play text-active text-muted"></i><i class="icon-pause text text-muted"></i></button></div>
                    </div>
                  </div>
                </div>
              </section>
            </div>
            <div class="col-6" id="data">
              <section class="panel" >
                <header class="panel-heading bg-white">
                  <div class="text-center h5">总计患者: <strong >{[total]}</strong></div>
                </header>
                <div class="pull-in text-center">
                  <div class="inline">
                    <div class="easypiechart" v-bind:data-percent="per" data-bar-color="#576879">
                      <span class="h2" style="margin-left:10px;margin-top:10px;display:inline-block">{[new_add]}</span>
                      <div class="easypie-text text-muted">新增患者</div>
                    </div>
                  </div>
                </div>
              </section>
            </div>
            <!-- easypiechart end-->
          </div>
          <section class="panel">
            <div class="text-muted l-h-2x">
              <span class="badge">3,3121</span>
              <span class="m-r-small">Orders</span>
              <span class="badge bg-success">25,129</span>
              <span class="m-r-small">Selling Items</span>
              <span class="badge">59,973</span>
              <span class="m-r-small">Items</span>
              <span class="badge">3,141</span> Customers
            </div>
          </section>
        </div>
        <div class="col-lg-6">
          <!-- sparkline stats -->
          <section class="panel">
            <header class="panel-heading">
              <ul class="nav nav-pills pull-right">
                <li><a href="#" data-loading-text="loading..."><i class="icon-retweet"></i></a></li>
                <li class="dropdown">
                  <a class="dropdown-toggle" data-toggle="dropdown" href="#">
                    <span class="text">Day</span> <span class="caret"></span>
                  </a>
                  <ul class="dropdown-menu pull-right">
                    <li class="active"><a href="#">Day</a></li>
                    <li><a href="#">Week</a></li>
                    <li><a href="#">Month</a></li>
                  </ul>
                </li>
              </ul>
              <span>今日快照</span>
            </header>
            <ul class="list-group list-group-flush m-t-n">
              <li class="list-group-item">
                <div class="media">
                  <div class="pull-left text-center media-large">
                    <div class="h4 m-t-mini"><strong>890</strong></div>
                    <small class="text-muted">总访问量</small>
                  </div>
                  <div class="pull-right hidden-sm text-right m-t">
                    <b class="badge bg-info" data-toggle="tooltip" data-title="New">250</b>
                  </div>
                  <div class="media-body">
                    <div class="sparkline" data-type="bar" data-bar-color="#8e98a9" data-bar-width="10" data-height="28"><!--20,10,15,21,12,5,21,30,24,15,8,19--></div>
                    <ul class="list-inline text-muted axis"><li>12<br>a</li><li>2</li><li>4</li><li>6</li><li>8</li><li>10</li><li>12<br>p</li><li>2</li><li>4</li><li>6</li><li>8</li><li>10</li></ul>
                  </div>
                </div>
              </li>
              <li class="list-group-item">
                <div class="media">
                  <div class="pull-left text-center media-large">
                    <div class="h4 m-t-mini"><strong>$4,800</strong></div>
                    <small class="text-muted">收入</small>
                  </div>
                  <div class="pull-right hidden-sm text-right m-t">
                    <b class="badge bg-success" data-toggle="tooltip" data-title="Captured">$4,000</b>
                  </div>
                  <div class="media-body">
                    <div class="sparkline" data-type="bar" data-bar-color="#13c4a5" data-bar-width="10" data-height="28"><!--200,400,500,100,90,1200,1500,1000,800,500,80,50--></div>
                    <ul class="list-inline text-muted axis"><li>12<br>a</li><li>2</li><li>4</li><li>6</li><li>8</li><li>10</li><li>12<br>p</li><li>2</li><li>4</li><li>6</li><li>8</li><li>10</li></ul>
                  </div>
                </div>
              </li>
              <li class="list-group-item">
                <div class="media">
                  <div class="pull-left text-center media-large">
                    <div class="h4 m-t-mini"><strong>595</strong></div>
                    <small class="text-muted">待办</small>
                  </div>
                  <div class="pull-right hidden-sm text-right m-t">
                    <b class="badge" data-toggle="tooltip" data-title="Awaiting">120<i class="icon-plane"></i></b>
                  </div>
                  <div class="media-body">
                    <div class="sparkline" data-type="bar" data-bar-color="#61a1e1" data-bar-width="10" data-height="28"><!--15,21,30,24,15,8,19,20,10,15,21,12--></div>
                    <ul class="list-inline text-muted axis"><li>12<br>a</li><li>2</li><li>4</li><li>6</li><li>8</li><li>10</li><li>12<br>p</li><li>2</li><li>4</li><li>6</li><li>8</li><li>10</li></ul>
                  </div>
                </div>
              </li>
            </ul>
          </section>
          <!-- sparkline stats end -->
        </div>        
      </div>
      <div class="row">
        <div class="col-lg-6">
          <!-- scrollable inbox widget -->
          <section class="panel">
            <header class="panel-heading">
              <ul class="nav nav-pills pull-right">
                <li>
                  <a href="#" class="panel-toggle text-muted"><i class="icon-caret-down icon-large text-active"></i><i class="icon-caret-up icon-large text"></i></a>
                </li>
              </ul>
              <span class="label label-large bg-default">15</span> 收件箱
            </header>
            <section style="height:214px" class="panel-content scrollbar scroll-y">
              <article class="media">
                <span class="pull-left thumb-small"><img src="../static/images/avatar.jpg" alt="John said" class="img-circle"></span>
                <div class="media-body">
                  <div class="pull-right media-mini text-center text-muted">
                    <strong class="h4">12:18</strong><br>
                    <small class="label bg-light">pm</small>
                  </div>
                    <a href="#" class="h4">来自患者康复回访的信件</a>
                  <small class="block"><a href="#" class="">张三</a> <span class="label label-success">Circle</span></small>
                  <small class="block">李医生妙手回春，杏林春暖，通过李医生的治疗，现在已经完全康复，支持！！</small>
                </div>
              </article>
              <div class="line pull-in"></div>
              <article class="media">
                <span class="pull-left thumb-small"><i class="icon-user icon-2x text-muted"></i></span>                
                <div class="media-body">
                  <div class="pull-right media-mini text-center text-muted">
                    <strong class="h4">17</strong><br>
                    <small class="label bg-light">feb</small>
                  </div>
                  <a href="#" class="h4">来着同事王医生的信件</a>
                  <small class="block"><a href="#" class="">王五</a> <span class="label label-info">同事</span></small>
                  <small class="block"> 患者404的信息已经交纳给您，请注意查收</small>
                </div>
              </article>
              <div class="line pull-in"></div>
              <article class="media">
                <div class="media-body">
                  <div class="pull-right media-mini text-center text-muted">
                    <strong class="h4">09</strong><br>
                    <small class="label bg-light">jan</small>
                  </div>
                  <a href="#" class="h4 text-success">更多信息请到邮件查阅</a>
                  <small class="block">Bootstrap, Ratchet</small>
                </div>
              </article>
            </section>
          </section>
          <!-- / scrollable inbox widget-->
        </div>
        <div class="col-lg-6">
          <!-- task -->
          <section class="panel">
            <header class="panel-heading">
              <a class="btn btn-link pull-right nav"><i class="icon-search text-default icon-large"></i></a>
              任务清单
            </header>
            <ul class="panel-content list-group list-group-flush m-t-n">
              <li class="list-group-item" data-toggle="class:active" data-target="#todo-1">
                <div class="media">
                  <span class="pull-left thumb-small m-t-mini">
                    <i class="icon-code icon-xlarge text-default"></i>
                  </span>
                  <div id="todo-1" class="pull-right text-primary m-t-small">
                    <i class="icon-circle icon-large text text-default"></i>
                    <i class="icon-ok-sign icon-large text-active text-primary"></i>
                  </div>
                  <div class="media-body">
                    <div><a href="#" class="h5">编程开发</a></div>
                    <small class="text-muted">9:30 am 每天</small>
                  </div>
                </div>
              </li>
              <li class="list-group-item bg" data-toggle="class:active" data-target="#todo-2">
                <div class="media">
                  <span class="pull-left thumb-small m-t-mini">
                    <i class="icon-reply icon-xlarge text-default"></i>
                  </span>
                  <div id="todo-2" class="pull-right text-primary m-t-small">
                    <i class="icon-circle icon-large text text-default"></i>
                    <i class="icon-ok-sign icon-large text-active text-primary"></i>
                  </div>
                  <div class="media-body">
                    <div><a href="#" class="h5">回复邮件</a></div>
                    <small class="text-muted">查阅邮件并回复</small>
                  </div>
                </div>
              </li>
              <li class="list-group-item" data-toggle="class:active" data-target="#todo-3">
                <div class="media">
                  <span class="pull-left thumb-small m-t-mini">
                    <i class="icon-coffee icon-xlarge text-default"></i>
                  </span>
                  <div id="todo-3" class="pull-right text-primary m-t-small">
                    <i class="icon-circle icon-large text text-default"></i>
                    <i class="icon-ok-sign icon-large text-active text-primary"></i>
                  </div>
                  <div class="media-body">
                    <div><a href="#" class="h5">咖啡时间</a></div>
                    <small class="text-muted">做你想做的事</small>
                  </div>
                </div>
              </li>
              <li class="list-group-item bg" data-toggle="class:active" data-target="#todo-4">
                <div class="media">
                  <span class="pull-left thumb-small m-t-mini">
                    <i class="icon-music icon-xlarge text-default"></i>
                  </span>
                  <div id="todo-4" class="pull-right text-primary m-t-small">
                    <i class="icon-circle icon-large text text-default"></i>
                    <i class="icon-ok-sign icon-large text-active text-primary"></i>
                  </div>
                  <div class="media-body">
                    <div><a href="#" class="h5">音乐时间</a></div>
                    <small class="text-muted">放松身体！</small>
                  </div>
                </div>
              </li>
            </ul>
          </section>
          <!-- / task-->
        </div>
        <div class="col-lg-12">
          <!-- .comment-list -->
<!--          <section class="comment-list block">-->
<!--            <article id="comment-id-1" class="comment-item media arrow arrow-left">-->
<!--              <a class="pull-left thumb-small avatar"><img src="../static/images/avatar.jpg" class="img-circle"></a>-->
<!--              <section class="media-body panel">-->
<!--                <header class="panel-heading clearfix">-->
<!--                  <a href="#">John smith</a><label class="label bg-info m-l-mini">Editor</label> <span class="text-muted m-l-small pull-right"><i class="icon-time"></i>24 minutes ago</span>-->
<!--                </header>-->
<!--                <div>Lorem ipsum dolor sit amet, consecteter adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.</div>-->
<!--                <div class="comment-action m-t-small">-->
<!--                  <a href="#" data-toggle="class" class="btn btn-white btn-mini active"><i class="icon-star-empty text-muted text"></i><i class="icon-star text-danger text-active"></i>Like</a>-->
<!--                  <a href="#comment-form" class="btn btn-white btn-mini"><i class="icon-mail-reply text-muted"></i>Reply</a>-->
<!--                </div>-->
<!--              </section>-->
<!--            </article>-->
<!--            &lt;!&ndash; .comment-reply &ndash;&gt;-->
<!--            <article id="comment-id-2" class="comment-item comment-reply media arrow arrow-left">-->
<!--              <a class="pull-left thumb-small"><span class="btn btn-circle btn-white btn-mini"><i class="icon-user"></i></span></a>-->
<!--              <section class="media-body panel text-small">-->
<!--                  <a href="#">Mika Sam</a><label class="label bg-inverse m-l-mini">Admin</label> Report this comment is helpful <span class="text-muted m-l-small pull-right"><i class="icon-time"></i>10 minutes ago</span>-->
<!--              </section>-->
<!--            </article>-->
<!--            &lt;!&ndash; / .comment-reply &ndash;&gt;-->
<!--            <article id="comment-id-3" class="comment-item media arrow arrow-left">-->
<!--              <a class="pull-left thumb-small avatar"><img src="../static/images/avatar.jpg" class="img-circle"></a>-->
<!--              <section class="media-body panel">-->
<!--                <header class="panel-heading clearfix">-->
<!--                  <a href="#">By me</a><label class="label bg-default m-l-mini">User</label> <span class="text-muted m-l-small pull-right"><i class="icon-time"></i>about 1 hour ago</span>-->
<!--                </header>-->
<!--                <div>This comment was posted by you.</div>-->
<!--                <div class="comment-action m-t-small">-->
<!--                  <a href="#comment-id-3" data-dismiss="alert" class="btn btn-white btn-mini"><i class="icon-trash text-muted"></i>Remove</a>-->
<!--                </div>-->
<!--              </section>-->
<!--            </article>-->
<!--            <article id="comment-id-4" class="comment-item media arrow arrow-left">-->
<!--              <a class="pull-left thumb-small avatar"><img src="../static/images/avatar.jpg" class="img-circle"></a>-->
<!--              <section class="media-body panel">-->
<!--                <header class="panel-heading clearfix">-->
<!--                  <a href="#">Peter</a><label class="label bg-primary m-l-mini">Vip</label> <span class="text-muted m-l-small pull-right"><i class="icon-time"></i>2 hours ago</span>-->
<!--                </header>-->
<!--                <blockquote>-->
<!--                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>-->
<!--                  <small>Someone famous in <cite title="Source Title">Source Title</cite></small>-->
<!--                </blockquote>-->
<!--                <div>Lorem ipsum dolor sit amet, consecteter adipiscing elit...</div>-->
<!--                <div class="comment-action m-t-small">-->
<!--                  <a href="#" data-toggle="class" class="btn btn-white btn-mini"><i class="icon-star-empty text-muted text"></i><i class="icon-star text-danger text-active"></i>Like</a>-->
<!--                  <a href="#comment-form" class="btn btn-white btn-mini"><i class="icon-mail-reply text-muted"></i>Reply</a>-->
<!--                </div>-->
<!--              </section>-->
<!--            </article>-->
<!--            &lt;!&ndash; comment form &ndash;&gt;-->
<!--            <article class="comment-item media" id="comment-form">-->
<!--              <a class="pull-left thumb-small avatar"><img src="../static/images/avatar.jpg" class="img-circle"></a>-->
<!--              <section class="media-body">-->
<!--                <form action="" class="m-b-none">-->
<!--                  <div class="input-group">-->
<!--                    <input type="text" placeholder="Input your comment here" class="form-control">-->
<!--                    <span class="input-group-btn">-->
<!--                      <button class="btn btn-primary" type="button">POST</button>-->
<!--                    </span>-->
<!--                  </div>-->
<!--                </form>-->
<!--              </section>-->
<!--            </article>-->
<!--          </section>-->
          <!-- / .comment-list -->
        </div>
      </div>
    </section>
  </section>
  <!-- .modal -->
  <div id="modal" class="modal fade">
    <form class="m-b-none">
      <div class="modal-dialog pos-abt" style="margin-top:-235px; top:50%">
        <div class="modal-content" id="postform">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true"><i class="icon-remove"></i></button>
            <h4 class="modal-title" id="myModalLabel">添加患者信息</h4>
          </div>
          <div class="modal-body" >
            <div class="block">
              <label class="control-label">姓名</label>
              <input type="text" class="form-control" placeholder="患者姓名" name="name" v-model="name">
              <label class="control-label">年龄</label>
              <input type="text" class="form-control" placeholder="患者年龄" name="age" v-model="age">
              <label class="control-label">血型</label>
              <select class="form-control " data-style="btn-info" @change="getBlood($event)">
                <option selected>请选择患者的血型</option>
                <option>O型血</option>
                <option>A型血</option>
                <option>B型血</option>
                <option>AB型血</option>
              </select>
              <label class="control-label">就医行为特征</label>
              <select class="form-control " data-style="btn-info" title="请选择患者就医行为特征" @change="getValue($event)">
                <option selected>请选择患者的行为特征</option>
                <option >就近方便型</option>
                <option >合同关系型</option>
                <option >人际关系型</option>
                <option >舆论诱导型</option>
                <option >信任医疗型</option>
                <option >高医疗消费型</option>
                <option >随意就医型</option>
              </select>
            </div>
            <div class="block">
              <label class="control-label">详细状况</label>
              <textarea class="form-control" placeholder="输入患者的详细状况" rows="5" v-model="detail"></textarea>
            </div>
<!--            <div class="checkbox">-->
<!--              <label>-->
<!--                <input type="checkbox"> Share with all memebers of first-->
<!--              </label>-->
<!--            </div>-->
          </div>
          <div class="modal-footer" >
            <button type="button" class="btn btn-small btn-default" data-dismiss="modal" >保存</button>
            <button type="button" class="btn btn-small btn-primary" data-dismiss="modal" @click="addUser()">提交</button>
          </div>
        </div><!-- /.modal-content -->
      </div>
    </form>
  </div>
  <!-- / .modal -->
  <!-- footer -->
  <footer id="footer">
    <div class="text-center padder clearfix">
      <p>
        <small>&copy; 基于Python的医疗数据可视化系统</small><br><br>
<!--        <a href="#" class="btn btn-mini btn-circle btn-twitter"><i class="icon-twitter"></i></a>-->
<!--        <a href="#" class="btn btn-mini btn-circle btn-facebook"><i class="icon-facebook"></i></a>-->
<!--        <a href="#" class="btn btn-mini btn-circle btn-gplus"><i class="icon-google-plus"></i></a>-->
      </p>
    </div>
  </footer>
  <a href="#" class="hide slide-nav-block" data-toggle="class:slide-nav slide-nav-left" data-target="body"></a>
  <!-- / footer -->
  {% block js %}
 	<script src="../static/js/jquery.min.js"></script>
  <!-- Bootstrap -->
  <script src="../static/js/bootstrap.js"></script>
  <!-- app -->
  <script src="../static/js/app.js"></script>
  <script src="../static/js/app.plugin.js"></script>
  <script src="../static/js/app.data.js"></script>

  <!-- Sparkline Chart -->
  <script src="../static/js/charts/sparkline/jquery.sparkline.min.js"></script>
  <!-- Easy Pie Chart -->
  <script src="../static/js/charts/easypiechart/jquery.easy-pie-chart.js"></script>
  <script src="../static/js/ie/excanvas.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script>
  <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script>
var postform=new Vue({
  el:'#postform',
  data:{
    name:'',
    age:'',
    blood:'',
    behavior:'',
    detail:''
  },
  methods:{
    addUser:function (){
      var postdata={
        name:this.name,
    age:parseInt(this.age),
    blood:this.blood,
    behavior:this.behavior,
    detail:this.detail,
      };
      this.name='';
      this.age='';
      this.blood='';
      this.behavior='';
      this.detail='';
      axios.post('/addPatient',postdata).then(
              function(response){
                alert("添加成功！")
                location.reload();
              }
      ).catch(function (error){
        alert(error);
      })
    },
    getValue:function (event){
      this.behavior=event.target.value;
    },
    getBlood:function (event){
       this.blood=event.target.value;
    }
  }
})
var statis=new Vue({
  delimiters: ['{[', ']}'],
  el:'#data',
  data:{
    total:'1555',
    new_add:'55',
    per:56,
  },
  mounted(){
    console.log(this.per)
    this.loadtotal();
  },
  methods: {
    loadtotal:function (){
      var that=this;
      axios.get('/statistics').then(function (response){
        that.total=response.data.total;
        that.new_add=response.data.new_add;
        that.per=parseInt(that.new_add)/parseInt(that.total);
        console.log(that.per)

      }).catch(function (error) {
        console.log(error)
      })
    }

  }
})



</script>
{% endblock %}
</body>
</html>