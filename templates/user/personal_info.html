<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>个人数据录入 - 智慧医疗</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        /* 顶部用户信息栏 */
        .top-user-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 6px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }

        /* 侧边导航栏 */
        .sidebar {
            position: fixed;
            top: 60px;
            left: 0;
            width: 250px;
            height: calc(100vh - 60px);
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 999;
        }

        .sidebar-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .sidebar-nav {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .sidebar-nav li {
            border-bottom: 1px solid #f0f0f0;
        }

        .sidebar-nav a {
            display: block;
            padding: 15px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-nav a:hover {
            background: #f8f9ff;
            color: #667eea;
            text-decoration: none;
        }

        .sidebar-nav a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .sidebar-nav i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .content-wrapper {
            margin-left: 250px;
            margin-top: 60px;
            padding: 30px;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        .section-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        .form-control {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 30px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .health-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .metric-card {
            background: #f8f9ff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- 顶部用户信息栏 -->
    <div class="top-user-bar">
        <div class="user-info">
            <div class="user-avatar">
                <i class="fa fa-user"></i>
            </div>
            <div>
                <div style="font-weight: 600;">{{ session.user or '演示用户' }}</div>
                <small style="opacity: 0.8;">个人数据录入</small>
            </div>
        </div>
        <div>
            <a href="{{ url_for('admin_dashboard') }}" class="logout-btn" style="margin-right: 10px;">
                <i class="fa fa-cog"></i> 管理后台
            </a>
            <a href="{{ url_for('logout') }}" class="logout-btn">
                <i class="fa fa-sign-out"></i> 退出登录
            </a>
        </div>
    </div>

    <!-- 侧边导航栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fa fa-heartbeat"></i> 功能导航</h4>
        </div>
        <ul class="sidebar-nav">
            <li>
                <a href="{{ url_for('user_dashboard') }}">
                    <i class="fa fa-home"></i> 用户中心首页
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_login_system') }}">
                    <i class="fa fa-sign-in"></i> 登录系统
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_personal_info') }}" class="active">
                    <i class="fa fa-edit"></i> 个人数据录入
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_query_report') }}">
                    <i class="fa fa-file-text"></i> 查看报告
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_health_dashboard') }}">
                    <i class="fa fa-dashboard"></i> 健康仪表盘
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_health_tips') }}">
                    <i class="fa fa-bell"></i> 健康日标提醒
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_health_archive') }}">
                    <i class="fa fa-folder"></i> 健康档案管理
                </a>
            </li>
        </ul>
    </div>

    <div class="content-wrapper">
        <div class="row">
                <div class="col-md-8">
                    <!-- 基本信息录入 -->
                    <div class="form-card">
                        <h3 class="section-title"><i class="fa fa-user"></i> 基本信息</h3>
                        <form>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>姓名</label>
                                        <input type="text" class="form-control" value="张三" placeholder="请输入姓名">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>年龄</label>
                                        <input type="number" class="form-control" value="28" placeholder="请输入年龄">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>性别</label>
                                        <select class="form-control">
                                            <option>男</option>
                                            <option>女</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>血型</label>
                                        <select class="form-control">
                                            <option>A型</option>
                                            <option>B型</option>
                                            <option>AB型</option>
                                            <option>O型</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 健康数据录入 -->
                    <div class="form-card">
                        <h3 class="section-title"><i class="fa fa-heartbeat"></i> 健康数据</h3>
                        <form>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>身高 (cm)</label>
                                        <input type="number" class="form-control" value="175" placeholder="请输入身高">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>体重 (kg)</label>
                                        <input type="number" class="form-control" value="70" placeholder="请输入体重">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>收缩压 (mmHg)</label>
                                        <input type="number" class="form-control" value="120" placeholder="请输入收缩压">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>舒张压 (mmHg)</label>
                                        <input type="number" class="form-control" value="80" placeholder="请输入舒张压">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>心率 (次/分)</label>
                                        <input type="number" class="form-control" value="72" placeholder="请输入心率">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>血糖 (mmol/L)</label>
                                        <input type="number" step="0.1" class="form-control" value="5.6" placeholder="请输入血糖">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 症状记录 -->
                    <div class="form-card">
                        <h3 class="section-title"><i class="fa fa-stethoscope"></i> 症状记录</h3>
                        <form>
                            <div class="form-group">
                                <label>主要症状</label>
                                <textarea class="form-control" rows="3" placeholder="请描述您的主要症状">轻微头痛，偶尔感到疲劳</textarea>
                            </div>
                            <div class="form-group">
                                <label>症状持续时间</label>
                                <select class="form-control">
                                    <option>1-2天</option>
                                    <option>3-7天</option>
                                    <option>1-2周</option>
                                    <option>超过2周</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>疼痛程度 (1-10分)</label>
                                <input type="range" class="form-control-range" min="1" max="10" value="3" id="painLevel">
                                <div class="text-center mt-2">
                                    <span class="badge badge-info">当前: <span id="painValue">3</span> 分</span>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="text-center">
                        <button type="button" class="btn btn-primary btn-lg" onclick="saveData()">
                            <i class="fa fa-save"></i> 保存数据
                        </button>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- 健康指标概览 -->
                    <div class="form-card">
                        <h4 class="section-title"><i class="fa fa-chart-line"></i> 健康指标</h4>
                        <div class="health-metrics">
                            <div class="metric-card">
                                <div class="metric-value">22.9</div>
                                <div class="metric-label">BMI指数</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">正常</div>
                                <div class="metric-label">血压状态</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">良好</div>
                                <div class="metric-label">心率状态</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">正常</div>
                                <div class="metric-label">血糖水平</div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近记录 -->
                    <div class="form-card">
                        <h4 class="section-title"><i class="fa fa-history"></i> 最近记录</h4>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item">
                                <small class="text-muted">2023-04-05</small><br>
                                <strong>体重记录</strong><br>
                                70kg
                            </div>
                            <div class="list-group-item">
                                <small class="text-muted">2023-04-03</small><br>
                                <strong>血压测量</strong><br>
                                120/80 mmHg
                            </div>
                            <div class="list-group-item">
                                <small class="text-muted">2023-04-01</small><br>
                                <strong>症状记录</strong><br>
                                轻微头痛
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        // 疼痛程度滑块
        $('#painLevel').on('input', function() {
            $('#painValue').text($(this).val());
        });

        // 保存数据
        function saveData() {
            // 模拟保存过程
            alert('数据保存成功！');
        }

        // 自动计算BMI
        function calculateBMI() {
            var height = parseFloat($('input[placeholder="请输入身高"]').val()) / 100;
            var weight = parseFloat($('input[placeholder="请输入体重"]').val());
            
            if (height && weight) {
                var bmi = (weight / (height * height)).toFixed(1);
                $('.metric-value').first().text(bmi);
            }
        }

        // 监听身高体重变化
        $('input[placeholder="请输入身高"], input[placeholder="请输入体重"]').on('input', calculateBMI);
    </script>
</body>
</html>
