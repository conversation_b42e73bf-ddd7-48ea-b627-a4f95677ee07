<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>个人数据录入 - 智慧医疗</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .navbar-custom .navbar-brand,
        .navbar-custom .navbar-nav .nav-link {
            color: white !important;
        }
        .content-wrapper {
            padding: 30px 0;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        .section-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        .form-control {
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 30px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .health-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .metric-card {
            background: #f8f9ff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('user_dashboard') }}">
                <i class="fa fa-heartbeat"></i> 智慧医疗
            </a>
            <div class="navbar-nav ml-auto">
                <a class="nav-link" href="{{ url_for('user_dashboard') }}">
                    <i class="fa fa-arrow-left"></i> 返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="content-wrapper">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <!-- 基本信息录入 -->
                    <div class="form-card">
                        <h3 class="section-title"><i class="fa fa-user"></i> 基本信息</h3>
                        <form>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>姓名</label>
                                        <input type="text" class="form-control" value="张三" placeholder="请输入姓名">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>年龄</label>
                                        <input type="number" class="form-control" value="28" placeholder="请输入年龄">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>性别</label>
                                        <select class="form-control">
                                            <option>男</option>
                                            <option>女</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>血型</label>
                                        <select class="form-control">
                                            <option>A型</option>
                                            <option>B型</option>
                                            <option>AB型</option>
                                            <option>O型</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 健康数据录入 -->
                    <div class="form-card">
                        <h3 class="section-title"><i class="fa fa-heartbeat"></i> 健康数据</h3>
                        <form>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>身高 (cm)</label>
                                        <input type="number" class="form-control" value="175" placeholder="请输入身高">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>体重 (kg)</label>
                                        <input type="number" class="form-control" value="70" placeholder="请输入体重">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>收缩压 (mmHg)</label>
                                        <input type="number" class="form-control" value="120" placeholder="请输入收缩压">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>舒张压 (mmHg)</label>
                                        <input type="number" class="form-control" value="80" placeholder="请输入舒张压">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>心率 (次/分)</label>
                                        <input type="number" class="form-control" value="72" placeholder="请输入心率">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>血糖 (mmol/L)</label>
                                        <input type="number" step="0.1" class="form-control" value="5.6" placeholder="请输入血糖">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 症状记录 -->
                    <div class="form-card">
                        <h3 class="section-title"><i class="fa fa-stethoscope"></i> 症状记录</h3>
                        <form>
                            <div class="form-group">
                                <label>主要症状</label>
                                <textarea class="form-control" rows="3" placeholder="请描述您的主要症状">轻微头痛，偶尔感到疲劳</textarea>
                            </div>
                            <div class="form-group">
                                <label>症状持续时间</label>
                                <select class="form-control">
                                    <option>1-2天</option>
                                    <option>3-7天</option>
                                    <option>1-2周</option>
                                    <option>超过2周</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>疼痛程度 (1-10分)</label>
                                <input type="range" class="form-control-range" min="1" max="10" value="3" id="painLevel">
                                <div class="text-center mt-2">
                                    <span class="badge badge-info">当前: <span id="painValue">3</span> 分</span>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="text-center">
                        <button type="button" class="btn btn-primary btn-lg" onclick="saveData()">
                            <i class="fa fa-save"></i> 保存数据
                        </button>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- 健康指标概览 -->
                    <div class="form-card">
                        <h4 class="section-title"><i class="fa fa-chart-line"></i> 健康指标</h4>
                        <div class="health-metrics">
                            <div class="metric-card">
                                <div class="metric-value">22.9</div>
                                <div class="metric-label">BMI指数</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">正常</div>
                                <div class="metric-label">血压状态</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">良好</div>
                                <div class="metric-label">心率状态</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value">正常</div>
                                <div class="metric-label">血糖水平</div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近记录 -->
                    <div class="form-card">
                        <h4 class="section-title"><i class="fa fa-history"></i> 最近记录</h4>
                        <div class="list-group list-group-flush">
                            <div class="list-group-item">
                                <small class="text-muted">2023-04-05</small><br>
                                <strong>体重记录</strong><br>
                                70kg
                            </div>
                            <div class="list-group-item">
                                <small class="text-muted">2023-04-03</small><br>
                                <strong>血压测量</strong><br>
                                120/80 mmHg
                            </div>
                            <div class="list-group-item">
                                <small class="text-muted">2023-04-01</small><br>
                                <strong>症状记录</strong><br>
                                轻微头痛
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        // 疼痛程度滑块
        $('#painLevel').on('input', function() {
            $('#painValue').text($(this).val());
        });

        // 保存数据
        function saveData() {
            // 模拟保存过程
            alert('数据保存成功！');
        }

        // 自动计算BMI
        function calculateBMI() {
            var height = parseFloat($('input[placeholder="请输入身高"]').val()) / 100;
            var weight = parseFloat($('input[placeholder="请输入体重"]').val());
            
            if (height && weight) {
                var bmi = (weight / (height * height)).toFixed(1);
                $('.metric-value').first().text(bmi);
            }
        }

        // 监听身高体重变化
        $('input[placeholder="请输入身高"], input[placeholder="请输入体重"]').on('input', calculateBMI);
    </script>
</body>
</html>
