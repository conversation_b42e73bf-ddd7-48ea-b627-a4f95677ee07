<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>查看报告 - 智慧医疗</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .navbar-custom .navbar-brand,
        .navbar-custom .navbar-nav .nav-link {
            color: white !important;
        }
        .content-wrapper {
            padding: 30px 0;
        }
        .report-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s;
        }
        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .report-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .report-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        .report-date {
            color: #666;
            font-size: 14px;
        }
        .report-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-normal {
            background-color: #d4edda;
            color: #155724;
        }
        .status-abnormal {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .report-content {
            margin-top: 15px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        .test-name {
            font-weight: 500;
            color: #333;
        }
        .test-value {
            font-weight: 600;
        }
        .test-normal {
            color: #28a745;
        }
        .test-abnormal {
            color: #dc3545;
        }
        .test-range {
            font-size: 12px;
            color: #666;
        }
        .filter-tabs {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }
        .tab-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            padding: 8px 20px;
            margin: 5px;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .tab-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }
        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 6px;
            color: white;
            padding: 6px 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('user_dashboard') }}">
                <i class="fa fa-heartbeat"></i> 智慧医疗
            </a>
            <div class="navbar-nav ml-auto">
                <a class="nav-link" href="{{ url_for('user_dashboard') }}">
                    <i class="fa fa-arrow-left"></i> 返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="content-wrapper">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h2><i class="fa fa-file-text"></i> 我的检查报告</h2>
                    <p class="text-muted">查看您的所有检查报告和化验结果</p>
                </div>
            </div>

            <!-- 筛选标签 -->
            <div class="filter-tabs">
                <div class="d-flex flex-wrap">
                    <button class="tab-btn active" onclick="filterReports('all')">全部报告</button>
                    <button class="tab-btn" onclick="filterReports('blood')">血液检查</button>
                    <button class="tab-btn" onclick="filterReports('imaging')">影像检查</button>
                    <button class="tab-btn" onclick="filterReports('urine')">尿液检查</button>
                    <button class="tab-btn" onclick="filterReports('other')">其他检查</button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <!-- 血常规报告 -->
                    <div class="report-card" data-type="blood">
                        <div class="report-header">
                            <div>
                                <h5 class="report-title">血常规检查</h5>
                                <div class="report-date">检查日期: 2023-04-05 09:30</div>
                            </div>
                            <div>
                                <span class="report-status status-normal">正常</span>
                                <button class="btn download-btn ml-2">
                                    <i class="fa fa-download"></i> 下载
                                </button>
                            </div>
                        </div>
                        <div class="report-content">
                            <div class="test-item">
                                <div>
                                    <div class="test-name">白细胞计数</div>
                                    <div class="test-range">参考范围: 4.0-10.0 ×10⁹/L</div>
                                </div>
                                <div class="test-value test-normal">6.8 ×10⁹/L</div>
                            </div>
                            <div class="test-item">
                                <div>
                                    <div class="test-name">红细胞计数</div>
                                    <div class="test-range">参考范围: 4.0-5.5 ×10¹²/L</div>
                                </div>
                                <div class="test-value test-normal">4.6 ×10¹²/L</div>
                            </div>
                            <div class="test-item">
                                <div>
                                    <div class="test-name">血红蛋白</div>
                                    <div class="test-range">参考范围: 120-160 g/L</div>
                                </div>
                                <div class="test-value test-normal">145 g/L</div>
                            </div>
                            <div class="test-item">
                                <div>
                                    <div class="test-name">血小板计数</div>
                                    <div class="test-range">参考范围: 100-300 ×10⁹/L</div>
                                </div>
                                <div class="test-value test-normal">250 ×10⁹/L</div>
                            </div>
                        </div>
                    </div>

                    <!-- 肝功能报告 -->
                    <div class="report-card" data-type="blood">
                        <div class="report-header">
                            <div>
                                <h5 class="report-title">肝功能检查</h5>
                                <div class="report-date">检查日期: 2023-04-03 14:20</div>
                            </div>
                            <div>
                                <span class="report-status status-abnormal">异常</span>
                                <button class="btn download-btn ml-2">
                                    <i class="fa fa-download"></i> 下载
                                </button>
                            </div>
                        </div>
                        <div class="report-content">
                            <div class="test-item">
                                <div>
                                    <div class="test-name">谷丙转氨酶(ALT)</div>
                                    <div class="test-range">参考范围: 5-40 U/L</div>
                                </div>
                                <div class="test-value test-abnormal">52 U/L ↑</div>
                            </div>
                            <div class="test-item">
                                <div>
                                    <div class="test-name">谷草转氨酶(AST)</div>
                                    <div class="test-range">参考范围: 8-40 U/L</div>
                                </div>
                                <div class="test-value test-normal">35 U/L</div>
                            </div>
                            <div class="test-item">
                                <div>
                                    <div class="test-name">总胆红素</div>
                                    <div class="test-range">参考范围: 3.4-20.5 μmol/L</div>
                                </div>
                                <div class="test-value test-normal">15.2 μmol/L</div>
                            </div>
                        </div>
                    </div>

                    <!-- 胸部X光报告 -->
                    <div class="report-card" data-type="imaging">
                        <div class="report-header">
                            <div>
                                <h5 class="report-title">胸部X光检查</h5>
                                <div class="report-date">检查日期: 2023-04-01 10:15</div>
                            </div>
                            <div>
                                <span class="report-status status-normal">正常</span>
                                <button class="btn download-btn ml-2">
                                    <i class="fa fa-download"></i> 下载
                                </button>
                            </div>
                        </div>
                        <div class="report-content">
                            <p><strong>影像所见:</strong></p>
                            <p>双肺纹理清晰，未见明显异常阴影。心影大小正常，膈面光滑。</p>
                            <p><strong>诊断意见:</strong></p>
                            <p>胸部X光检查未见异常。</p>
                        </div>
                    </div>

                    <!-- 尿常规报告 -->
                    <div class="report-card" data-type="urine">
                        <div class="report-header">
                            <div>
                                <h5 class="report-title">尿常规检查</h5>
                                <div class="report-date">检查日期: 2023-03-30 08:45</div>
                            </div>
                            <div>
                                <span class="report-status status-pending">待审核</span>
                                <button class="btn download-btn ml-2" disabled>
                                    <i class="fa fa-clock-o"></i> 待审核
                                </button>
                            </div>
                        </div>
                        <div class="report-content">
                            <div class="test-item">
                                <div>
                                    <div class="test-name">蛋白质</div>
                                    <div class="test-range">参考范围: 阴性</div>
                                </div>
                                <div class="test-value test-normal">阴性(-)</div>
                            </div>
                            <div class="test-item">
                                <div>
                                    <div class="test-name">葡萄糖</div>
                                    <div class="test-range">参考范围: 阴性</div>
                                </div>
                                <div class="test-value test-normal">阴性(-)</div>
                            </div>
                            <div class="test-item">
                                <div>
                                    <div class="test-name">白细胞</div>
                                    <div class="test-range">参考范围: 0-5个/HP</div>
                                </div>
                                <div class="test-value test-normal">2个/HP</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- 报告统计 -->
                    <div class="report-card">
                        <h5><i class="fa fa-chart-pie"></i> 报告统计</h5>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>总报告数</span>
                                <span class="font-weight-bold">12</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>正常报告</span>
                                <span class="text-success font-weight-bold">8</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>异常报告</span>
                                <span class="text-danger font-weight-bold">3</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>待审核</span>
                                <span class="text-warning font-weight-bold">1</span>
                            </div>
                        </div>
                    </div>

                    <!-- 医生建议 -->
                    <div class="report-card">
                        <h5><i class="fa fa-user-md"></i> 医生建议</h5>
                        <div class="mt-3">
                            <div class="alert alert-warning">
                                <small><strong>注意:</strong> 肝功能检查显示ALT轻度升高，建议：</small>
                                <ul class="mb-0 mt-2">
                                    <li>避免饮酒</li>
                                    <li>清淡饮食</li>
                                    <li>充足休息</li>
                                    <li>2周后复查</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="report-card">
                        <h5><i class="fa fa-bolt"></i> 快捷操作</h5>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-block mb-2">
                                <i class="fa fa-calendar"></i> 预约复查
                            </button>
                            <button class="btn btn-outline-success btn-block mb-2">
                                <i class="fa fa-phone"></i> 咨询医生
                            </button>
                            <button class="btn btn-outline-info btn-block">
                                <i class="fa fa-download"></i> 批量下载
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        function filterReports(type) {
            // 更新标签状态
            $('.tab-btn').removeClass('active');
            event.target.classList.add('active');
            
            // 筛选报告
            if (type === 'all') {
                $('.report-card').show();
            } else {
                $('.report-card').hide();
                $('.report-card[data-type="' + type + '"]').show();
            }
        }
    </script>
</body>
</html>
