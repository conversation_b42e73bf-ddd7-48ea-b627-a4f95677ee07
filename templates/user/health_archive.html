<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>健康档案管理 - 智慧医疗</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .navbar-custom .navbar-brand, .navbar-custom .navbar-nav .nav-link {
            color: white !important;
        }
        .content-wrapper { padding: 30px 0; }
        .archive-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 20px;
        }
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            text-align: center;
        }
        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 48px;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #dee2e6;
        }
        .timeline-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        .timeline-date {
            color: #667eea;
            font-weight: bold;
            font-size: 14px;
        }
        .timeline-title {
            font-weight: 600;
            margin: 5px 0;
        }
        .timeline-desc {
            color: #666;
            font-size: 14px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .info-item {
            background: #f8f9ff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .medication-item {
            background: #f8f9ff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .medication-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        .tab-content {
            margin-top: 20px;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #666;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('user_dashboard') }}">
                <i class="fa fa-heartbeat"></i> 智慧医疗
            </a>
            <div class="navbar-nav ml-auto">
                <a class="nav-link" href="{{ url_for('user_dashboard') }}">
                    <i class="fa fa-arrow-left"></i> 返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="content-wrapper">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h2><i class="fa fa-folder"></i> 健康档案管理</h2>
                    <p class="text-muted">管理您的完整健康档案信息</p>
                </div>
            </div>

            <!-- 个人档案头部 -->
            <div class="profile-header">
                <div class="profile-avatar">
                    <i class="fa fa-user"></i>
                </div>
                <h3>{{ session.user or '张三' }}</h3>
                <p>档案编号: P2023040001 | 创建时间: 2023-01-15</p>
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div><strong>28</strong></div>
                        <small>年龄</small>
                    </div>
                    <div class="col-md-3">
                        <div><strong>A型</strong></div>
                        <small>血型</small>
                    </div>
                    <div class="col-md-3">
                        <div><strong>175cm</strong></div>
                        <small>身高</small>
                    </div>
                    <div class="col-md-3">
                        <div><strong>70kg</strong></div>
                        <small>体重</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <!-- 标签页导航 -->
                    <ul class="nav nav-tabs" id="archiveTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="medical-tab" data-toggle="tab" href="#medical" role="tab">
                                <i class="fa fa-stethoscope"></i> 就诊记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="medication-tab" data-toggle="tab" href="#medication" role="tab">
                                <i class="fa fa-medkit"></i> 用药记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="examination-tab" data-toggle="tab" href="#examination" role="tab">
                                <i class="fa fa-file-text"></i> 检查记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="allergy-tab" data-toggle="tab" href="#allergy" role="tab">
                                <i class="fa fa-exclamation-triangle"></i> 过敏史
                            </a>
                        </li>
                    </ul>

                    <!-- 标签页内容 -->
                    <div class="tab-content" id="archiveTabContent">
                        <!-- 就诊记录 -->
                        <div class="tab-pane fade show active" id="medical" role="tabpanel">
                            <div class="archive-card">
                                <h5><i class="fa fa-history"></i> 就诊时间线</h5>
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <div class="timeline-content">
                                            <div class="timeline-date">2023-04-05</div>
                                            <div class="timeline-title">内科门诊 - 王建国医生</div>
                                            <div class="timeline-desc">
                                                主诉：轻微头痛，偶感疲劳<br>
                                                诊断：上呼吸道感染<br>
                                                处理：开具感冒药，建议多休息
                                            </div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-content">
                                            <div class="timeline-date">2023-03-20</div>
                                            <div class="timeline-title">体检中心 - 年度体检</div>
                                            <div class="timeline-desc">
                                                体检项目：血常规、肝功能、胸片、心电图<br>
                                                结果：大部分指标正常，肝功能轻微异常<br>
                                                建议：注意休息，减少熬夜
                                            </div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-content">
                                            <div class="timeline-date">2023-02-15</div>
                                            <div class="timeline-title">眼科门诊 - 刘志强医生</div>
                                            <div class="timeline-desc">
                                                主诉：视力下降，眼部疲劳<br>
                                                诊断：轻度近视<br>
                                                处理：配镜，注意用眼卫生
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 用药记录 -->
                        <div class="tab-pane fade" id="medication" role="tabpanel">
                            <div class="archive-card">
                                <h5><i class="fa fa-pills"></i> 当前用药</h5>
                                <div class="mt-3">
                                    <div class="medication-item">
                                        <div class="medication-icon">
                                            <i class="fa fa-medkit"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="font-weight-bold">维生素C片</div>
                                            <small class="text-muted">每日1次，每次2片，饭后服用</small><br>
                                            <small class="text-info">开始时间: 2023-04-01 | 持续: 30天</small>
                                        </div>
                                        <div class="text-right">
                                            <span class="badge badge-success">进行中</span>
                                        </div>
                                    </div>
                                    <div class="medication-item">
                                        <div class="medication-icon">
                                            <i class="fa fa-medkit"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="font-weight-bold">感冒灵颗粒</div>
                                            <small class="text-muted">每日3次，每次1袋，温水冲服</small><br>
                                            <small class="text-info">开始时间: 2023-04-05 | 持续: 3天</small>
                                        </div>
                                        <div class="text-right">
                                            <span class="badge badge-warning">即将结束</span>
                                        </div>
                                    </div>
                                </div>

                                <h5 class="mt-4"><i class="fa fa-history"></i> 历史用药</h5>
                                <div class="mt-3">
                                    <div class="medication-item">
                                        <div class="medication-icon" style="background: #6c757d;">
                                            <i class="fa fa-medkit"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="font-weight-bold">布洛芬缓释胶囊</div>
                                            <small class="text-muted">每日2次，每次1粒，疼痛时服用</small><br>
                                            <small class="text-info">使用时间: 2023-03-15 - 2023-03-20</small>
                                        </div>
                                        <div class="text-right">
                                            <span class="badge badge-secondary">已完成</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 检查记录 -->
                        <div class="tab-pane fade" id="examination" role="tabpanel">
                            <div class="archive-card">
                                <h5><i class="fa fa-file-text"></i> 检查报告汇总</h5>
                                <div class="table-responsive mt-3">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>检查日期</th>
                                                <th>检查项目</th>
                                                <th>检查结果</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>2023-04-05</td>
                                                <td>血常规</td>
                                                <td>各项指标正常</td>
                                                <td><span class="badge badge-success">正常</span></td>
                                                <td><button class="btn btn-sm btn-outline-primary">查看</button></td>
                                            </tr>
                                            <tr>
                                                <td>2023-04-03</td>
                                                <td>肝功能</td>
                                                <td>ALT轻度升高</td>
                                                <td><span class="badge badge-warning">异常</span></td>
                                                <td><button class="btn btn-sm btn-outline-primary">查看</button></td>
                                            </tr>
                                            <tr>
                                                <td>2023-04-01</td>
                                                <td>胸部X光</td>
                                                <td>双肺纹理清晰</td>
                                                <td><span class="badge badge-success">正常</span></td>
                                                <td><button class="btn btn-sm btn-outline-primary">查看</button></td>
                                            </tr>
                                            <tr>
                                                <td>2023-03-30</td>
                                                <td>心电图</td>
                                                <td>窦性心律</td>
                                                <td><span class="badge badge-success">正常</span></td>
                                                <td><button class="btn btn-sm btn-outline-primary">查看</button></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 过敏史 -->
                        <div class="tab-pane fade" id="allergy" role="tabpanel">
                            <div class="archive-card">
                                <h5><i class="fa fa-exclamation-triangle"></i> 过敏信息</h5>
                                <div class="mt-3">
                                    <div class="alert alert-warning">
                                        <strong><i class="fa fa-warning"></i> 药物过敏</strong><br>
                                        <ul class="mb-0 mt-2">
                                            <li>青霉素 - 皮疹、瘙痒</li>
                                            <li>阿司匹林 - 胃肠道不适</li>
                                        </ul>
                                    </div>
                                    <div class="alert alert-info">
                                        <strong><i class="fa fa-info-circle"></i> 食物过敏</strong><br>
                                        <ul class="mb-0 mt-2">
                                            <li>海鲜类 - 皮肤红肿</li>
                                            <li>芒果 - 口唇肿胀</li>
                                        </ul>
                                    </div>
                                    <div class="alert alert-secondary">
                                        <strong><i class="fa fa-leaf"></i> 环境过敏</strong><br>
                                        <ul class="mb-0 mt-2">
                                            <li>花粉 - 鼻塞、流涕</li>
                                            <li>尘螨 - 打喷嚏</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- 基本信息 -->
                    <div class="archive-card">
                        <h5><i class="fa fa-user"></i> 基本信息</h5>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">性别</div>
                                <div class="info-value">男</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">年龄</div>
                                <div class="info-value">28岁</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">血型</div>
                                <div class="info-value">A型</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">BMI</div>
                                <div class="info-value">22.9</div>
                            </div>
                        </div>
                    </div>

                    <!-- 紧急联系人 -->
                    <div class="archive-card">
                        <h5><i class="fa fa-phone"></i> 紧急联系人</h5>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>姓名</span>
                                <span class="font-weight-bold">李女士</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>关系</span>
                                <span class="font-weight-bold">配偶</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>电话</span>
                                <span class="font-weight-bold">138****8888</span>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="archive-card">
                        <h5><i class="fa fa-bolt"></i> 快捷操作</h5>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-block mb-2">
                                <i class="fa fa-edit"></i> 编辑档案
                            </button>
                            <button class="btn btn-outline-success btn-block mb-2">
                                <i class="fa fa-download"></i> 导出档案
                            </button>
                            <button class="btn btn-outline-info btn-block mb-2">
                                <i class="fa fa-share"></i> 分享给医生
                            </button>
                            <button class="btn btn-outline-warning btn-block">
                                <i class="fa fa-print"></i> 打印档案
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
</body>
</html>
