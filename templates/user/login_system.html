<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>登录系统 - 智慧医疗</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .navbar-custom .navbar-brand, .navbar-custom .navbar-nav .nav-link {
            color: white !important;
        }
        .content-wrapper { padding: 30px 0; }
        .system-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }
        .system-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        .system-icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: #667eea;
        }
        .system-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .system-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .system-status {
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 15px;
        }
        .status-online {
            background-color: #d4edda;
            color: #155724;
        }
        .status-maintenance {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-offline {
            background-color: #f8d7da;
            color: #721c24;
        }
        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 12px 30px;
            font-weight: 500;
            width: 100%;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .login-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .quick-access {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
        }
        .access-item {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .access-item:hover {
            background: rgba(255,255,255,0.2);
        }
        .access-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('user_dashboard') }}">
                <i class="fa fa-heartbeat"></i> 智慧医疗
            </a>
            <div class="navbar-nav ml-auto">
                <a class="nav-link" href="{{ url_for('user_dashboard') }}">
                    <i class="fa fa-arrow-left"></i> 返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="content-wrapper">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h2><i class="fa fa-sign-in"></i> 系统登录中心</h2>
                    <p class="text-muted">选择您要登录的医疗系统</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="row">
                        <!-- 医院信息系统 -->
                        <div class="col-md-6">
                            <div class="system-card" onclick="loginToSystem('his')">
                                <div class="system-icon">
                                    <i class="fa fa-hospital-o"></i>
                                </div>
                                <div class="system-title">医院信息系统</div>
                                <div class="system-status status-online">
                                    <i class="fa fa-circle"></i> 在线
                                </div>
                                <div class="system-desc">
                                    查看个人就诊记录、预约挂号、在线缴费等医院相关服务
                                </div>
                                <button class="btn login-btn">
                                    <i class="fa fa-sign-in"></i> 立即登录
                                </button>
                            </div>
                        </div>

                        <!-- 健康管理系统 -->
                        <div class="col-md-6">
                            <div class="system-card" onclick="loginToSystem('health')">
                                <div class="system-icon">
                                    <i class="fa fa-heartbeat"></i>
                                </div>
                                <div class="system-title">健康管理系统</div>
                                <div class="system-status status-online">
                                    <i class="fa fa-circle"></i> 在线
                                </div>
                                <div class="system-desc">
                                    个人健康档案管理、健康数据分析、健康目标设定等功能
                                </div>
                                <button class="btn login-btn">
                                    <i class="fa fa-sign-in"></i> 立即登录
                                </button>
                            </div>
                        </div>

                        <!-- 检验报告系统 -->
                        <div class="col-md-6">
                            <div class="system-card" onclick="loginToSystem('lab')">
                                <div class="system-icon">
                                    <i class="fa fa-file-text"></i>
                                </div>
                                <div class="system-title">检验报告系统</div>
                                <div class="system-status status-online">
                                    <i class="fa fa-circle"></i> 在线
                                </div>
                                <div class="system-desc">
                                    查看检验报告、化验结果、影像资料等医学检查信息
                                </div>
                                <button class="btn login-btn">
                                    <i class="fa fa-sign-in"></i> 立即登录
                                </button>
                            </div>
                        </div>

                        <!-- 预约挂号系统 -->
                        <div class="col-md-6">
                            <div class="system-card" onclick="loginToSystem('appointment')">
                                <div class="system-icon">
                                    <i class="fa fa-calendar"></i>
                                </div>
                                <div class="system-title">预约挂号系统</div>
                                <div class="system-status status-maintenance">
                                    <i class="fa fa-wrench"></i> 维护中
                                </div>
                                <div class="system-desc">
                                    在线预约专家号、普通号，查看医生排班信息
                                </div>
                                <button class="btn login-btn" disabled>
                                    <i class="fa fa-wrench"></i> 系统维护
                                </button>
                            </div>
                        </div>

                        <!-- 在线咨询系统 -->
                        <div class="col-md-6">
                            <div class="system-card" onclick="loginToSystem('consult')">
                                <div class="system-icon">
                                    <i class="fa fa-comments"></i>
                                </div>
                                <div class="system-title">在线咨询系统</div>
                                <div class="system-status status-online">
                                    <i class="fa fa-circle"></i> 在线
                                </div>
                                <div class="system-desc">
                                    与医生在线交流、远程问诊、健康咨询等服务
                                </div>
                                <button class="btn login-btn">
                                    <i class="fa fa-sign-in"></i> 立即登录
                                </button>
                            </div>
                        </div>

                        <!-- 药品管理系统 -->
                        <div class="col-md-6">
                            <div class="system-card" onclick="loginToSystem('pharmacy')">
                                <div class="system-icon">
                                    <i class="fa fa-medkit"></i>
                                </div>
                                <div class="system-title">药品管理系统</div>
                                <div class="system-status status-offline">
                                    <i class="fa fa-times-circle"></i> 离线
                                </div>
                                <div class="system-desc">
                                    查看处方药品、用药指导、药品配送等服务
                                </div>
                                <button class="btn login-btn" disabled>
                                    <i class="fa fa-times"></i> 暂不可用
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- 快速访问 -->
                    <div class="quick-access">
                        <h4><i class="fa fa-bolt"></i> 快速访问</h4>
                        <p>常用功能快捷入口</p>
                        
                        <div class="access-item" onclick="quickAccess('emergency')">
                            <div class="access-icon">
                                <i class="fa fa-ambulance"></i>
                            </div>
                            <div>
                                <div class="font-weight-bold">急诊绿色通道</div>
                                <small>紧急情况快速就医</small>
                            </div>
                        </div>

                        <div class="access-item" onclick="quickAccess('report')">
                            <div class="access-icon">
                                <i class="fa fa-file-text"></i>
                            </div>
                            <div>
                                <div class="font-weight-bold">最新报告</div>
                                <small>查看最新检查结果</small>
                            </div>
                        </div>

                        <div class="access-item" onclick="quickAccess('appointment')">
                            <div class="access-icon">
                                <i class="fa fa-calendar-check-o"></i>
                            </div>
                            <div>
                                <div class="font-weight-bold">我的预约</div>
                                <small>查看预约记录</small>
                            </div>
                        </div>

                        <div class="access-item" onclick="quickAccess('medicine')">
                            <div class="access-icon">
                                <i class="fa fa-pills"></i>
                            </div>
                            <div>
                                <div class="font-weight-bold">用药提醒</div>
                                <small>查看用药计划</small>
                            </div>
                        </div>
                    </div>

                    <!-- 系统公告 -->
                    <div class="system-card">
                        <h5><i class="fa fa-bullhorn"></i> 系统公告</h5>
                        <div class="text-left mt-3">
                            <div class="alert alert-info">
                                <small><strong>系统升级通知</strong><br>
                                预约挂号系统将于今晚22:00-次日6:00进行维护升级。</small>
                            </div>
                            <div class="alert alert-success">
                                <small><strong>新功能上线</strong><br>
                                健康管理系统新增AI健康评估功能，欢迎体验！</small>
                            </div>
                            <div class="alert alert-warning">
                                <small><strong>安全提醒</strong><br>
                                请妥善保管您的登录密码，不要泄露给他人。</small>
                            </div>
                        </div>
                    </div>

                    <!-- 帮助中心 -->
                    <div class="system-card">
                        <h5><i class="fa fa-question-circle"></i> 需要帮助？</h5>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-block mb-2">
                                <i class="fa fa-book"></i> 使用指南
                            </button>
                            <button class="btn btn-outline-success btn-block mb-2">
                                <i class="fa fa-phone"></i> 客服热线
                            </button>
                            <button class="btn btn-outline-info btn-block">
                                <i class="fa fa-envelope"></i> 在线客服
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        function loginToSystem(systemType) {
            // 模拟登录过程
            const systemNames = {
                'his': '医院信息系统',
                'health': '健康管理系统',
                'lab': '检验报告系统',
                'appointment': '预约挂号系统',
                'consult': '在线咨询系统',
                'pharmacy': '药品管理系统'
            };

            const systemName = systemNames[systemType];
            
            if (systemType === 'appointment') {
                alert('系统维护中，暂时无法登录！');
                return;
            }
            
            if (systemType === 'pharmacy') {
                alert('系统离线，暂时无法登录！');
                return;
            }

            // 显示登录进度
            const originalText = event.target.innerHTML;
            event.target.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 登录中...';
            event.target.disabled = true;

            setTimeout(() => {
                alert(`成功登录${systemName}！\n\n这是演示系统，实际使用时会跳转到对应的系统页面。`);
                event.target.innerHTML = originalText;
                event.target.disabled = false;
            }, 2000);
        }

        function quickAccess(type) {
            const accessNames = {
                'emergency': '急诊绿色通道',
                'report': '最新报告',
                'appointment': '我的预约',
                'medicine': '用药提醒'
            };

            alert(`正在打开${accessNames[type]}...`);
        }

        // 页面加载完成后的初始化
        $(document).ready(function() {
            // 添加系统状态检查动画
            $('.status-online').prepend('<i class="fa fa-circle text-success"></i> ');
            $('.status-maintenance').prepend('<i class="fa fa-wrench text-warning"></i> ');
            $('.status-offline').prepend('<i class="fa fa-times-circle text-danger"></i> ');
        });
    </script>
</body>
</html>
