<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>健康仪表盘 - 智慧医疗</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .navbar-custom .navbar-brand, .navbar-custom .navbar-nav .nav-link {
            color: white !important;
        }
        .content-wrapper { padding: 30px 0; }
        .dashboard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 20px;
            height: 100%;
        }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .metric-value {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .chart-container {
            height: 300px;
            position: relative;
        }
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#667eea 0deg 252deg, #e9ecef 252deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            position: relative;
        }
        .progress-circle::before {
            content: '';
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }
        .progress-text {
            position: relative;
            z-index: 1;
            font-weight: bold;
            color: #333;
        }
        .health-score {
            text-align: center;
            padding: 20px;
        }
        .score-number {
            font-size: 48px;
            font-weight: bold;
            color: #667eea;
        }
        .trend-up {
            color: #28a745;
        }
        .trend-down {
            color: #dc3545;
        }
        .trend-stable {
            color: #6c757d;
        }
        .activity-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }
        .activity-icon.exercise {
            background: #28a745;
        }
        .activity-icon.meal {
            background: #fd7e14;
        }
        .activity-icon.sleep {
            background: #6f42c1;
        }
        .activity-icon.medicine {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('user_dashboard') }}">
                <i class="fa fa-heartbeat"></i> 智慧医疗
            </a>
            <div class="navbar-nav ml-auto">
                <a class="nav-link" href="{{ url_for('user_dashboard') }}">
                    <i class="fa fa-arrow-left"></i> 返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="content-wrapper">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h2><i class="fa fa-dashboard"></i> 健康仪表盘</h2>
                    <p class="text-muted">全面了解您的健康状况和趋势</p>
                </div>
            </div>

            <!-- 健康指标卡片 -->
            <div class="row">
                <div class="col-md-3">
                    <div class="metric-card">
                        <div class="metric-value">98</div>
                        <div class="metric-label">健康评分</div>
                        <small><i class="fa fa-arrow-up trend-up"></i> +2 较上周</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                        <div class="metric-value">120/80</div>
                        <div class="metric-label">血压 (mmHg)</div>
                        <small><i class="fa fa-minus trend-stable"></i> 稳定</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card" style="background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);">
                        <div class="metric-value">72</div>
                        <div class="metric-label">心率 (次/分)</div>
                        <small><i class="fa fa-arrow-down trend-down"></i> -3 较昨日</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card" style="background: linear-gradient(135deg, #e83e8c 0%, #fd7e14 100%);">
                        <div class="metric-value">22.9</div>
                        <div class="metric-label">BMI指数</div>
                        <small><i class="fa fa-arrow-up trend-up"></i> +0.1 较上月</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- 健康趋势图 -->
                <div class="col-md-8">
                    <div class="dashboard-card">
                        <h5><i class="fa fa-line-chart"></i> 健康趋势</h5>
                        <div class="chart-container">
                            <canvas id="healthTrendChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 健康评分 -->
                <div class="col-md-4">
                    <div class="dashboard-card">
                        <h5><i class="fa fa-trophy"></i> 综合健康评分</h5>
                        <div class="health-score">
                            <div class="progress-circle">
                                <div class="progress-text">
                                    <div class="score-number">98</div>
                                    <div>优秀</div>
                                </div>
                            </div>
                            <p class="text-muted">您的健康状况非常良好！</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- 今日活动 -->
                <div class="col-md-6">
                    <div class="dashboard-card">
                        <h5><i class="fa fa-calendar-check-o"></i> 今日活动</h5>
                        <div class="mt-3">
                            <div class="activity-item">
                                <div class="activity-icon exercise">
                                    <i class="fa fa-running"></i>
                                </div>
                                <div>
                                    <div class="font-weight-bold">晨跑</div>
                                    <small class="text-muted">07:00 - 07:30 | 3.2公里</small>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon meal">
                                    <i class="fa fa-cutlery"></i>
                                </div>
                                <div>
                                    <div class="font-weight-bold">早餐</div>
                                    <small class="text-muted">08:00 | 燕麦粥、鸡蛋、牛奶</small>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon medicine">
                                    <i class="fa fa-medkit"></i>
                                </div>
                                <div>
                                    <div class="font-weight-bold">服药提醒</div>
                                    <small class="text-muted">09:00 | 维生素C</small>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon sleep">
                                    <i class="fa fa-bed"></i>
                                </div>
                                <div>
                                    <div class="font-weight-bold">睡眠质量</div>
                                    <small class="text-muted">昨晚 | 8小时15分钟 - 优质</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 健康建议 -->
                <div class="col-md-6">
                    <div class="dashboard-card">
                        <h5><i class="fa fa-lightbulb-o"></i> 个性化建议</h5>
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <strong><i class="fa fa-info-circle"></i> 运动建议</strong><br>
                                <small>您今天的运动量已达标，建议继续保持每日30分钟的有氧运动。</small>
                            </div>
                            <div class="alert alert-success">
                                <strong><i class="fa fa-check-circle"></i> 饮食建议</strong><br>
                                <small>营养摄入均衡，建议增加蔬菜和水果的摄入量。</small>
                            </div>
                            <div class="alert alert-warning">
                                <strong><i class="fa fa-exclamation-triangle"></i> 健康提醒</strong><br>
                                <small>您的肝功能指标略有异常，建议减少熬夜，保证充足睡眠。</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="row">
                <div class="col-12">
                    <div class="dashboard-card">
                        <h5><i class="fa fa-bolt"></i> 快捷操作</h5>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary btn-block" onclick="location.href='{{ url_for('user_personal_info') }}'">
                                    <i class="fa fa-plus"></i><br>录入数据
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success btn-block">
                                    <i class="fa fa-calendar"></i><br>预约体检
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-info btn-block" onclick="location.href='{{ url_for('user_query_report') }}'">
                                    <i class="fa fa-file-text"></i><br>查看报告
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning btn-block">
                                    <i class="fa fa-phone"></i><br>在线咨询
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 健康趋势图
        const ctx = document.getElementById('healthTrendChart').getContext('2d');
        const healthTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '健康评分',
                    data: [85, 88, 92, 95, 96, 98],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'BMI指数',
                    data: [23.5, 23.2, 23.0, 22.8, 22.9, 22.9],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });

        // 动态更新时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN');
            document.title = `健康仪表盘 - ${timeString} - 智慧医疗`;
        }
        
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
