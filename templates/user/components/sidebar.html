<!-- 顶部用户信息栏 -->
<div class="top-user-bar">
    <div class="user-info">
        <button class="mobile-menu-btn" onclick="toggleSidebar()">
            <i class="fa fa-bars"></i>
        </button>
        <div class="user-avatar">
            <i class="fa fa-user"></i>
        </div>
        <div>
            <div style="font-weight: 600;">{{ session.user or '演示用户' }}</div>
            <small style="opacity: 0.8;">{{ page_title or '智慧医疗用户中心' }}</small>
        </div>
    </div>
    <div>
        <a href="{{ url_for('admin_dashboard') }}" class="logout-btn" style="margin-right: 10px;">
            <i class="fa fa-cog"></i> 管理后台
        </a>
        <a href="{{ url_for('logout') }}" class="logout-btn">
            <i class="fa fa-sign-out"></i> 退出登录
        </a>
    </div>
</div>

<!-- 侧边导航栏 -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <h4><i class="fa fa-heartbeat"></i> 功能导航</h4>
    </div>
    <ul class="sidebar-nav">
        <li>
            <a href="{{ url_for('user_dashboard') }}" class="{{ 'active' if current_page == 'dashboard' else '' }}">
                <i class="fa fa-home"></i> 用户中心首页
            </a>
        </li>
        <li>
            <a href="{{ url_for('user_login_system') }}" class="{{ 'active' if current_page == 'login_system' else '' }}">
                <i class="fa fa-sign-in"></i> 登录系统
            </a>
        </li>
        <li>
            <a href="{{ url_for('user_personal_info') }}" class="{{ 'active' if current_page == 'personal_info' else '' }}">
                <i class="fa fa-edit"></i> 个人数据录入
            </a>
        </li>
        <li>
            <a href="{{ url_for('user_query_report') }}" class="{{ 'active' if current_page == 'query_report' else '' }}">
                <i class="fa fa-file-text"></i> 查看报告
            </a>
        </li>
        <li>
            <a href="{{ url_for('user_health_dashboard') }}" class="{{ 'active' if current_page == 'health_dashboard' else '' }}">
                <i class="fa fa-dashboard"></i> 健康仪表盘
            </a>
        </li>
        <li>
            <a href="{{ url_for('user_health_tips') }}" class="{{ 'active' if current_page == 'health_tips' else '' }}">
                <i class="fa fa-bell"></i> 健康日标提醒
            </a>
        </li>
        <li>
            <a href="{{ url_for('user_health_archive') }}" class="{{ 'active' if current_page == 'health_archive' else '' }}">
                <i class="fa fa-folder"></i> 健康档案管理
            </a>
        </li>
    </ul>
</div>

<script>
// 移动端侧边栏切换
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('show');
}

// 点击页面其他地方关闭侧边栏
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('sidebar');
    const menuBtn = document.querySelector('.mobile-menu-btn');
    
    if (window.innerWidth <= 768 && 
        !sidebar.contains(event.target) && 
        !menuBtn.contains(event.target)) {
        sidebar.classList.remove('show');
    }
});
</script>
