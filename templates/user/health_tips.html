<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>健康日标提醒 - 智慧医疗</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body { background-color: #f8f9fa; }
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .navbar-custom .navbar-brand, .navbar-custom .navbar-nav .nav-link {
            color: white !important;
        }
        .content-wrapper { padding: 30px 0; }
        .tips-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 20px;
        }
        .goal-card {
            background: white;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        .goal-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .goal-progress {
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .goal-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s;
        }
        .reminder-item {
            background: #f8f9ff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .reminder-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }
        .reminder-icon.medicine {
            background: #dc3545;
        }
        .reminder-icon.exercise {
            background: #28a745;
        }
        .reminder-icon.meal {
            background: #fd7e14;
        }
        .reminder-icon.checkup {
            background: #6f42c1;
        }
        .reminder-time {
            font-weight: bold;
            color: #667eea;
        }
        .achievement-badge {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin: 5px;
        }
        .tip-of-day {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .btn-complete {
            background: #28a745;
            border: none;
            color: white;
            border-radius: 6px;
            padding: 5px 15px;
            font-size: 12px;
        }
        .btn-snooze {
            background: #6c757d;
            border: none;
            color: white;
            border-radius: 6px;
            padding: 5px 15px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('user_dashboard') }}">
                <i class="fa fa-heartbeat"></i> 智慧医疗
            </a>
            <div class="navbar-nav ml-auto">
                <a class="nav-link" href="{{ url_for('user_dashboard') }}">
                    <i class="fa fa-arrow-left"></i> 返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="content-wrapper">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h2><i class="fa fa-bell"></i> 健康日标提醒</h2>
                    <p class="text-muted">设置健康目标，接收个性化提醒</p>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <!-- 每日健康小贴士 -->
                    <div class="tip-of-day">
                        <h4><i class="fa fa-lightbulb-o"></i> 今日健康小贴士</h4>
                        <p class="mb-0">多喝水有助于新陈代谢，建议每天饮水量不少于8杯（约2000ml）。适量的水分摄入可以帮助排毒、维持皮肤健康，并提高身体机能。</p>
                    </div>

                    <!-- 今日提醒 -->
                    <div class="tips-card">
                        <h5><i class="fa fa-clock-o"></i> 今日提醒事项</h5>
                        <div class="mt-3">
                            <div class="reminder-item">
                                <div class="reminder-icon medicine">
                                    <i class="fa fa-medkit"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="font-weight-bold">服药提醒</div>
                                    <small class="text-muted">维生素C - 每日一次，饭后服用</small>
                                </div>
                                <div>
                                    <div class="reminder-time">09:00</div>
                                    <button class="btn btn-complete btn-sm mt-1">完成</button>
                                </div>
                            </div>

                            <div class="reminder-item">
                                <div class="reminder-icon exercise">
                                    <i class="fa fa-running"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="font-weight-bold">运动提醒</div>
                                    <small class="text-muted">晨跑30分钟 - 保持心率在120-140之间</small>
                                </div>
                                <div>
                                    <div class="reminder-time">07:00</div>
                                    <button class="btn btn-complete btn-sm mt-1">完成</button>
                                </div>
                            </div>

                            <div class="reminder-item">
                                <div class="reminder-icon meal">
                                    <i class="fa fa-cutlery"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="font-weight-bold">饮食提醒</div>
                                    <small class="text-muted">午餐时间 - 注意营养均衡，少油少盐</small>
                                </div>
                                <div>
                                    <div class="reminder-time">12:00</div>
                                    <button class="btn btn-snooze btn-sm mt-1">稍后</button>
                                </div>
                            </div>

                            <div class="reminder-item">
                                <div class="reminder-icon checkup">
                                    <i class="fa fa-stethoscope"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="font-weight-bold">复查提醒</div>
                                    <small class="text-muted">肝功能复查 - 距离上次检查已2周</small>
                                </div>
                                <div>
                                    <div class="reminder-time">明天</div>
                                    <button class="btn btn-complete btn-sm mt-1">预约</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 健康目标 -->
                    <div class="tips-card">
                        <h5><i class="fa fa-target"></i> 我的健康目标</h5>
                        <div class="mt-3">
                            <div class="goal-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">每日步数目标</h6>
                                        <small class="text-muted">目标: 10,000步</small>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-weight-bold">7,500步</div>
                                        <small class="text-success">75%</small>
                                    </div>
                                </div>
                                <div class="goal-progress">
                                    <div class="goal-progress-bar" style="width: 75%"></div>
                                </div>
                            </div>

                            <div class="goal-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">每日饮水量</h6>
                                        <small class="text-muted">目标: 2000ml</small>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-weight-bold">1,600ml</div>
                                        <small class="text-warning">80%</small>
                                    </div>
                                </div>
                                <div class="goal-progress">
                                    <div class="goal-progress-bar" style="width: 80%"></div>
                                </div>
                            </div>

                            <div class="goal-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">睡眠时间</h6>
                                        <small class="text-muted">目标: 8小时</small>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-weight-bold">8.5小时</div>
                                        <small class="text-success">106%</small>
                                    </div>
                                </div>
                                <div class="goal-progress">
                                    <div class="goal-progress-bar" style="width: 100%"></div>
                                </div>
                            </div>

                            <div class="goal-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">体重控制</h6>
                                        <small class="text-muted">目标: 68kg</small>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-weight-bold">70kg</div>
                                        <small class="text-danger">+2kg</small>
                                    </div>
                                </div>
                                <div class="goal-progress">
                                    <div class="goal-progress-bar" style="width: 90%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- 成就徽章 -->
                    <div class="tips-card">
                        <h5><i class="fa fa-trophy"></i> 我的成就</h5>
                        <div class="mt-3">
                            <div class="achievement-badge">
                                <i class="fa fa-star"></i> 连续运动7天
                            </div>
                            <div class="achievement-badge">
                                <i class="fa fa-heart"></i> 健康达人
                            </div>
                            <div class="achievement-badge">
                                <i class="fa fa-clock-o"></i> 按时服药
                            </div>
                            <div class="achievement-badge">
                                <i class="fa fa-leaf"></i> 健康饮食
                            </div>
                        </div>
                    </div>

                    <!-- 本周统计 -->
                    <div class="tips-card">
                        <h5><i class="fa fa-bar-chart"></i> 本周统计</h5>
                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>完成提醒</span>
                                <span class="font-weight-bold text-success">28/30</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>运动天数</span>
                                <span class="font-weight-bold text-primary">6/7</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>按时服药</span>
                                <span class="font-weight-bold text-success">7/7</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>睡眠质量</span>
                                <span class="font-weight-bold text-info">优秀</span>
                            </div>
                        </div>
                    </div>

                    <!-- 健康建议 -->
                    <div class="tips-card">
                        <h5><i class="fa fa-lightbulb-o"></i> 个性化建议</h5>
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <small><strong>运动建议:</strong><br>
                                您的运动量很好，建议增加力量训练。</small>
                            </div>
                            <div class="alert alert-warning">
                                <small><strong>饮食提醒:</strong><br>
                                注意控制体重，减少高热量食物摄入。</small>
                            </div>
                            <div class="alert alert-success">
                                <small><strong>睡眠质量:</strong><br>
                                睡眠质量很好，请继续保持。</small>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷设置 -->
                    <div class="tips-card">
                        <h5><i class="fa fa-cog"></i> 快捷设置</h5>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-block mb-2">
                                <i class="fa fa-plus"></i> 添加提醒
                            </button>
                            <button class="btn btn-outline-success btn-block mb-2">
                                <i class="fa fa-target"></i> 设置目标
                            </button>
                            <button class="btn btn-outline-info btn-block">
                                <i class="fa fa-bell"></i> 通知设置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        // 完成提醒
        $('.btn-complete').click(function() {
            $(this).closest('.reminder-item').fadeOut(300);
            // 这里可以添加AJAX请求来更新服务器状态
        });

        // 稍后提醒
        $('.btn-snooze').click(function() {
            $(this).text('已延后').prop('disabled', true);
        });

        // 动态更新进度条
        function updateProgress() {
            $('.goal-progress-bar').each(function() {
                const width = $(this).css('width');
                $(this).css('width', '0').animate({width: width}, 1000);
            });
        }

        $(document).ready(function() {
            updateProgress();
        });
    </script>
</body>
</html>
