<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>智慧医疗 - 用户中心</title>
    <meta name="description" content="智慧医疗患者用户中心">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .navbar-custom .navbar-brand,
        .navbar-custom .navbar-nav .nav-link {
            color: white !important;
        }
        .navbar-custom .navbar-nav .nav-link:hover {
            color: #f8f9fa !important;
        }
        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .module-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            transition: all 0.3s;
            cursor: pointer;
            border: none;
        }
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        .module-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #667eea;
        }
        .module-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .module-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
        }
        .stats-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
        }
        .stats-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 12px 24px;
            margin: 5px;
            font-weight: 500;
            transition: all 0.3s;
        }
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-heartbeat"></i> 智慧医疗用户中心
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fa fa-user"></i> 个人中心</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#"><i class="fa fa-bell"></i> 消息通知</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin_dashboard') }}"><i class="fa fa-cog"></i> 管理后台</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}"><i class="fa fa-sign-out"></i> 退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 欢迎区域 -->
    <section class="welcome-section">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <h1><i class="fa fa-user-circle"></i> 欢迎回来，{{ session.user or '用户' }}！</h1>
                    <p class="lead">您的健康管理助手，为您提供全方位的医疗健康服务</p>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number">98%</div>
                        <div class="stats-label">健康指数</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要内容 -->
    <div class="container">
        <!-- 快捷操作 -->
        <div class="quick-actions">
            <h4><i class="fa fa-bolt"></i> 快捷操作</h4>
            <div class="mt-3">
                <button class="btn action-btn" onclick="location.href='{{ url_for('user_personal_info') }}'">
                    <i class="fa fa-plus"></i> 录入健康数据
                </button>
                <button class="btn action-btn" onclick="location.href='{{ url_for('user_query_report') }}'">
                    <i class="fa fa-search"></i> 查看检查报告
                </button>
                <button class="btn action-btn" onclick="location.href='#'">
                    <i class="fa fa-calendar"></i> 预约挂号
                </button>
                <button class="btn action-btn" onclick="location.href='#'">
                    <i class="fa fa-phone"></i> 在线咨询
                </button>
            </div>
        </div>

        <!-- 功能模块 -->
        <div class="row">
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_login_system') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-sign-in"></i>
                        </div>
                        <div class="module-title">登录系统</div>
                        <div class="module-desc">
                            快速登录医疗系统，查看个人医疗信息和预约记录
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_personal_info') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-edit"></i>
                        </div>
                        <div class="module-title">个人数据录入</div>
                        <div class="module-desc">
                            录入和更新个人健康数据，包括体检结果、症状记录等
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_query_report') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-file-text"></i>
                        </div>
                        <div class="module-title">查看报告</div>
                        <div class="module-desc">
                            查看检查报告、化验结果和医生诊断建议
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_health_dashboard') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-dashboard"></i>
                        </div>
                        <div class="module-title">看健康仪表盘</div>
                        <div class="module-desc">
                            直观查看健康趋势图表和各项健康指标分析
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_health_tips') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-bell"></i>
                        </div>
                        <div class="module-title">健康日标提醒</div>
                        <div class="module-desc">
                            设置健康目标，接收个性化的健康提醒和建议
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_health_archive') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-folder"></i>
                        </div>
                        <div class="module-title">健康档案管理</div>
                        <div class="module-desc">
                            管理个人健康档案，查看历史就诊记录和用药情况
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="mt-5 py-4" style="background-color: #343a40; color: white;">
        <div class="container text-center">
            <p>&copy; 2023 智慧医疗患者管理系统. 保留所有权利.</p>
            <p><small>为您的健康保驾护航</small></p>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        // 添加一些交互效果
        $(document).ready(function() {
            // 模块卡片悬停效果
            $('.module-card').hover(
                function() {
                    $(this).find('.module-icon').addClass('animated pulse');
                },
                function() {
                    $(this).find('.module-icon').removeClass('animated pulse');
                }
            );
            
            // 显示欢迎消息
            setTimeout(function() {
                if (typeof toastr !== 'undefined') {
                    toastr.success('欢迎使用智慧医疗系统！', '登录成功');
                }
            }, 1000);
        });
    </script>
</body>
</html>
