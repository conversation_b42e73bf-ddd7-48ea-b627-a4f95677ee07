<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>智慧医疗 - 用户中心</title>
    <meta name="description" content="智慧医疗患者用户中心">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        /* 顶部用户信息栏 */
        .top-user-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 6px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            text-decoration: none;
        }

        /* 侧边导航栏 */
        .sidebar {
            position: fixed;
            top: 60px;
            left: 0;
            width: 250px;
            height: calc(100vh - 60px);
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 999;
        }

        .sidebar-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .sidebar-nav {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .sidebar-nav li {
            border-bottom: 1px solid #f0f0f0;
        }

        .sidebar-nav a {
            display: block;
            padding: 15px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-nav a:hover {
            background: #f8f9ff;
            color: #667eea;
            text-decoration: none;
        }

        .sidebar-nav a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .sidebar-nav i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 250px;
            margin-top: 60px;
            padding: 30px;
            min-height: calc(100vh - 60px);
        }

        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .module-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            transition: all 0.3s;
            cursor: pointer;
            border: none;
            height: 280px; /* 固定高度 */
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .module-icon {
            font-size: 48px;
            margin-bottom: 20px;
            color: #667eea;
        }

        .module-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }

        .module-desc {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            height: 120px; /* 固定高度 */
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .stats-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
        }

        .stats-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }

        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 12px 24px;
            margin: 5px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .module-card {
                height: auto;
                min-height: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部用户信息栏 -->
    <div class="top-user-bar">
        <div class="user-info">
            <div class="user-avatar">
                <i class="fa fa-user"></i>
            </div>
            <div>
                <div style="font-weight: 600;">{{ session.user or '演示用户' }}</div>
                <small style="opacity: 0.8;">智慧医疗用户中心</small>
            </div>
        </div>
        <div>
            <a href="{{ url_for('admin_dashboard') }}" class="logout-btn" style="margin-right: 10px;">
                <i class="fa fa-cog"></i> 管理后台
            </a>
            <a href="{{ url_for('logout') }}" class="logout-btn">
                <i class="fa fa-sign-out"></i> 退出登录
            </a>
        </div>
    </div>

    <!-- 侧边导航栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4><i class="fa fa-heartbeat"></i> 功能导航</h4>
        </div>
        <ul class="sidebar-nav">
            <li>
                <a href="{{ url_for('user_dashboard') }}" class="active">
                    <i class="fa fa-home"></i> 用户中心首页
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_login_system') }}">
                    <i class="fa fa-sign-in"></i> 登录系统
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_personal_info') }}">
                    <i class="fa fa-edit"></i> 个人数据录入
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_query_report') }}">
                    <i class="fa fa-file-text"></i> 查看报告
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_health_dashboard') }}">
                    <i class="fa fa-dashboard"></i> 健康仪表盘
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_health_tips') }}">
                    <i class="fa fa-bell"></i> 健康日标提醒
                </a>
            </li>
            <li>
                <a href="{{ url_for('user_health_archive') }}">
                    <i class="fa fa-folder"></i> 健康档案管理
                </a>
            </li>
        </ul>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- 欢迎区域 -->
        <section class="welcome-section">
            <div class="row">
                <div class="col-md-8">
                    <h1><i class="fa fa-user-circle"></i> 欢迎回来，{{ session.user or '用户' }}！</h1>
                    <p class="lead">您的健康管理助手，为您提供全方位的医疗健康服务</p>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-number">98%</div>
                        <div class="stats-label">健康指数</div>
                    </div>
                </div>
            </div>
        </section>
        <!-- 快捷操作 -->
        <div class="quick-actions">
            <h4><i class="fa fa-bolt"></i> 快捷操作</h4>
            <div class="mt-3">
                <button class="btn action-btn" onclick="location.href='{{ url_for('user_personal_info') }}'">
                    <i class="fa fa-plus"></i> 录入健康数据
                </button>
                <button class="btn action-btn" onclick="location.href='{{ url_for('user_query_report') }}'">
                    <i class="fa fa-search"></i> 查看检查报告
                </button>
                <button class="btn action-btn" onclick="location.href='#'">
                    <i class="fa fa-calendar"></i> 预约挂号
                </button>
                <button class="btn action-btn" onclick="location.href='#'">
                    <i class="fa fa-phone"></i> 在线咨询
                </button>
            </div>
        </div>

        <!-- 功能模块 -->
        <div class="row">
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_login_system') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-sign-in"></i>
                        </div>
                        <div class="module-title">登录系统</div>
                        <div class="module-desc">
                            快速登录医疗系统，查看个人医疗信息和预约记录
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_personal_info') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-edit"></i>
                        </div>
                        <div class="module-title">个人数据录入</div>
                        <div class="module-desc">
                            录入和更新个人健康数据，包括体检结果、症状记录等
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_query_report') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-file-text"></i>
                        </div>
                        <div class="module-title">查看报告</div>
                        <div class="module-desc">
                            查看检查报告、化验结果和医生诊断建议
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_health_dashboard') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-dashboard"></i>
                        </div>
                        <div class="module-title">看健康仪表盘</div>
                        <div class="module-desc">
                            直观查看健康趋势图表和各项健康指标分析
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_health_tips') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-bell"></i>
                        </div>
                        <div class="module-title">健康日标提醒</div>
                        <div class="module-desc">
                            设置健康目标，接收个性化的健康提醒和建议
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="module-card" onclick="location.href='{{ url_for('user_health_archive') }}'">
                    <div class="text-center">
                        <div class="module-icon">
                            <i class="fa fa-folder"></i>
                        </div>
                        <div class="module-title">健康档案管理</div>
                        <div class="module-desc">
                            管理个人健康档案，查看历史就诊记录和用药情况
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="mt-5 py-4" style="background-color: #343a40; color: white; margin-left: 250px;">
        <div class="text-center">
            <p>&copy; 2023 智慧医疗患者管理系统. 保留所有权利.</p>
            <p><small>为您的健康保驾护航</small></p>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        // 添加一些交互效果
        $(document).ready(function() {
            // 模块卡片悬停效果
            $('.module-card').hover(
                function() {
                    $(this).find('.module-icon').addClass('animated pulse');
                },
                function() {
                    $(this).find('.module-icon').removeClass('animated pulse');
                }
            );
            
            // 显示欢迎消息
            setTimeout(function() {
                if (typeof toastr !== 'undefined') {
                    toastr.success('欢迎使用智慧医疗系统！', '登录成功');
                }
            }, 1000);
        });
    </script>
</body>
</html>
