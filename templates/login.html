<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>智慧医疗患者管理系统 - 登录</title>
    <meta name="description" content="智慧医疗患者管理系统登录页面">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-control {
            height: 45px;
            border-radius: 8px;
            border: 1px solid #ddd;
            padding: 0 15px;
            font-size: 14px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            width: 100%;
            height: 45px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 20px;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ddd;
        }
        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 15px;
            color: #666;
            font-size: 14px;
        }
        .register-link {
            text-align: center;
        }
        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .register-link a:hover {
            text-decoration: underline;
        }
        .alert {
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .system-info {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        .system-info h4 {
            color: #333;
            margin-bottom: 10px;
        }
        .system-info p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="system-info">
                        <h4><i class="fa fa-heartbeat text-danger"></i> 智慧医疗患者管理系统</h4>
                        <p>为您提供专业的医疗健康管理服务</p>
                    </div>
                    
                    <div class="login-card">
                        <div class="login-header">
                            <h2>系统登录</h2>
                            <p>请输入您的登录凭据</p>
                        </div>
                        
                        {% if error %}
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle"></i> {{ error }}
                        </div>
                        {% endif %}
                        
                        <form method="POST" action="{{ url_for('login') }}">
                            <div class="form-group">
                                <label for="username">用户名</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="请输入用户名" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">密码</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="请输入密码" required>
                            </div>
                            
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember">
                                    <label class="form-check-label" for="remember">
                                        记住我
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-login">
                                <i class="fa fa-sign-in"></i> 登录系统
                            </button>
                        </form>
                        
                        <div class="divider">
                            <span>或者</span>
                        </div>
                        
                        <div class="register-link">
                            <p>还没有账户？ <a href="{{ url_for('register') }}">立即注册</a></p>
                        </div>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="fa fa-info-circle"></i> 
                                演示系统：输入任意用户名和密码即可登录
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script>
        // 简单的表单验证
        $('form').on('submit', function(e) {
            var username = $('#username').val().trim();
            var password = $('#password').val().trim();
            
            if (!username || !password) {
                e.preventDefault();
                alert('请输入用户名和密码！');
                return false;
            }
        });
        
        // 添加一些演示提示
        $(document).ready(function() {
            // 可以添加一些提示信息
            setTimeout(function() {
                if (!$('.alert').length) {
                    $('.login-header').after(
                        '<div class="alert alert-info">' +
                        '<i class="fa fa-lightbulb-o"></i> ' +
                        '提示：这是演示系统，输入任意用户名和密码即可登录体验！' +
                        '</div>'
                    );
                }
            }, 2000);
        });
    </script>
</body>
</html>
