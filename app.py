# author:axbros
from flask import Flask,render_template,request,jsonify,redirect,url_for,session
import utils
import json
app=Flask(__name__)
app.secret_key = 'patient_management_system_2023'  # 用于session

@app.route('/')
def index():
    # 检查是否已登录，如果已登录直接进入对应页面
    if 'user' in session:
        if session.get('user_type') == 'admin':
            return redirect(url_for('admin_dashboard'))
        else:
            return redirect(url_for('user_dashboard'))
    # 未登录显示登录页面
    return render_template('login.html')

@app.route('/login_page')
def login_page():
    return render_template('login.html')

@app.route('/login', methods=['POST'])
def login():
    # 假的登录验证，任何输入都可以成功
    username = request.form.get('username')
    password = request.form.get('password')

    if username and password:  # 只要有输入就算成功
        session['user'] = username
        session['user_type'] = 'admin'  # 默认管理员身份
        return redirect(url_for('admin_dashboard'))
    else:
        return render_template('login.html', error='请输入用户名和密码')

@app.route('/quick_demo')
def quick_demo():
    # 快速演示入口，自动设置session
    session['user'] = 'demo_user'
    session['user_type'] = 'admin'
    return redirect(url_for('admin_index'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        # 假的注册，任何输入都可以成功
        username = request.form.get('username')
        password = request.form.get('password')
        email = request.form.get('email')

        if username and password:  # 只要有输入就算成功
            session['user'] = username
            session['user_type'] = 'user'  # 普通用户身份
            return redirect(url_for('user_dashboard'))
        else:
            return render_template('register.html', error='请填写完整信息')

    return render_template('register.html')

@app.route('/admin')
def admin_dashboard():
    if 'user' not in session:
        return redirect(url_for('login_page'))
    return render_template('index.html')

@app.route('/user')
def user_dashboard():
    if 'user' not in session:
        return redirect(url_for('login_page'))
    return render_template('user_dashboard.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login_page'))

@app.route('/addPatient',methods=['POST'])
def addPatient():
    data=json.loads(request.get_data().decode('utf-8'))
    Gname=data['name']
    Gage=data['age']
    Gblood=data['blood']
    Gbehavior=data['behavior']
    Gdetail=data['detail']
    res=utils.addPatient(Gname,Gage,Gblood,Gbehavior,Gdetail)
    ret_dic={}
    if res == 'success':
        ret_dic['msg']='添加成功！'
        return jsonify(ret_dic)
    else:
        ret_dic['msg'] = '添加失败'
        return jsonify(ret_dic)
#总计患者与今日新增
@app.route('/statistics')
def statistics():
    res=utils.statistics()
    return jsonify(res)
#展示病人信息
@app.route('/show')
def show():
    info_list=utils.show_patient_info()
    return jsonify({'data':info_list})
@app.route('/table')
def table():
    return render_template('table.html')
@app.route('/addrep',methods=['POST'])
def addrep():
    res=json.loads(request.get_data().decode('utf-8'))
    utils.addresp(res['name'],res['detail'],res['resp'])
    return res
@app.route('/chart')
def chart():
    stat = utils.statistics()

    total="%.2f"%((stat.get('new_add')/stat.get('total'))*100)

    return render_template('chart.html',new_add=stat.get('new_add'),total=total)
@app.route('/mypie')
def getMypie():
    return jsonify(utils.getMypie())
@app.route('/removeUser')
def removeUser():
    user_name=request.args.get('name')
    utils.removeUser(user_name)
    return jsonify({'state':'ok'})
@app.route('/search_user')
def searchUser():
    username=request.args.get('name')
    res=utils.searchname(username)
    return jsonify({'data':res})
@app.route('/calendar')
def calendar():
    return render_template('calendar.html')
@app.route('/relation_data')
def get_relation_data():
    return render_template('graph_base.html')
@app.route('/relations')
def relations():
    return render_template('relations.html')

# ============================
# 前台用户模块路由
# ============================

@app.route('/user/login_system')
def user_login_system():
    """用户登录系统"""
    if 'user' not in session:
        return redirect(url_for('login_page'))
    return render_template('user/login_system.html')

@app.route('/user/personal_info')
def user_personal_info():
    """个人数据录入"""
    if 'user' not in session:
        return redirect(url_for('login_page'))
    return render_template('user/personal_info.html')

@app.route('/user/query_report')
def user_query_report():
    """查看报告"""
    if 'user' not in session:
        return redirect(url_for('login_page'))
    return render_template('user/query_report.html')

@app.route('/user/health_dashboard')
def user_health_dashboard():
    """看健康仪表盘"""
    if 'user' not in session:
        return redirect(url_for('login_page'))
    return render_template('user/health_dashboard.html')

@app.route('/user/health_tips')
def user_health_tips():
    """健康日标提醒"""
    if 'user' not in session:
        return redirect(url_for('login_page'))
    return render_template('user/health_tips.html')

@app.route('/user/health_archive')
def user_health_archive():
    """健康档案管理"""
    if 'user' not in session:
        return redirect(url_for('login_page'))
    return render_template('user/health_archive.html')

# 在后台管理页面添加进入前台的链接
@app.route('/admin/index')
def admin_index():
    """管理员首页，重定向到原来的index"""
    if 'user' not in session:
        return redirect(url_for('login_page'))
    return render_template('admin_index.html')

if __name__ == '__main__':
    app.run(port=8887)
