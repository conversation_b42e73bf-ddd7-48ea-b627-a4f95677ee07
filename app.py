# author:axbros
from flask import Flask,render_template,request,jsonify
import utils
import json
app=Flask(__name__)
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/addPatient',methods=['POST'])
def addPatient():
    data=json.loads(request.get_data().decode('utf-8'))
    Gname=data['name']
    Gage=data['age']
    Gblood=data['blood']
    Gbehavior=data['behavior']
    Gdetail=data['detail']
    res=utils.addPatient(Gname,Gage,Gblood,Gbehavior,Gdetail)
    ret_dic={}
    if res == 'success':
        ret_dic['msg']='添加成功！'
        return jsonify(ret_dic)
    else:
        ret_dic['msg'] = '添加失败'
        return jsonify(ret_dic)
#总计患者与今日新增
@app.route('/statistics')
def statistics():
    res=utils.statistics()
    return jsonify(res)
#展示病人信息
@app.route('/show')
def show():
    info_list=utils.show_patient_info()
    return jsonify({'data':info_list})
@app.route('/table')
def table():
    return render_template('table.html')
@app.route('/addrep',methods=['POST'])
def addrep():
    res=json.loads(request.get_data().decode('utf-8'))
    utils.addresp(res['name'],res['detail'],res['resp'])
    return res
@app.route('/chart')
def chart():
    stat = utils.statistics()

    total="%.2f"%((stat.get('new_add')/stat.get('total'))*100)

    return render_template('chart.html',new_add=stat.get('new_add'),total=total)
@app.route('/mypie')
def getMypie():
    return jsonify(utils.getMypie())
@app.route('/removeUser')
def removeUser():
    user_name=request.args.get('name')
    utils.removeUser(user_name)
    return jsonify({'state':'ok'})
@app.route('/search_user')
def searchUser():
    username=request.args.get('name')
    res=utils.searchname(username)
    return jsonify({'data':res})
@app.route('/calendar')
def calendar():
    return render_template('calendar.html')
@app.route('/relation_data')
def get_relation_data():
    return render_template('graph_base.html')
@app.route('/relations')
def relations():

    return render_template('relations.html')


if __name__ == '__main__':
    app.run(port=8887)
