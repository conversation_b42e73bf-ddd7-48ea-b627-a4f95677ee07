# author:axbros
# 模拟数据库连接，用于演示
import json
import os
from datetime import datetime

# 模拟数据存储文件
DATA_FILE = 'mock_data.json'

# 初始化模拟数据
def init_mock_data():
    if not os.path.exists(DATA_FILE):
        mock_data = {
            'users': [
                {
                    'id': 1,
                    'name': '张三',
                    'age': 28,
                    'blood': 'A型血',
                    'behavior': '就近方便型',
                    'detail': '轻微头痛，偶感疲劳',
                    'date': '2023-04-01',
                    'doc_resp': '建议多休息，注意饮食'
                },
                {
                    'id': 2,
                    'name': '李四',
                    'age': 35,
                    'blood': 'B型血',
                    'behavior': '信任医疗型',
                    'detail': '胸部疼痛，呼吸困难',
                    'date': '2023-04-02',
                    'doc_resp': '需要进一步检查'
                },
                {
                    'id': 3,
                    'name': '王五',
                    'age': 42,
                    'blood': 'O型血',
                    'behavior': '高医疗消费型',
                    'detail': '腰部疼痛，活动受限',
                    'date': '2023-04-03',
                    'doc_resp': '建议物理治疗'
                }
            ]
        }
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(mock_data, f, ensure_ascii=False, indent=2)

def load_data():
    init_mock_data()
    with open(DATA_FILE, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_data(data):
    with open(DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def addPatient(Rname,Rage,Rblood,Rbehavior,Rdetail):
    data = load_data()
    new_id = max([user['id'] for user in data['users']], default=0) + 1
    new_user = {
        'id': new_id,
        'name': Rname,
        'age': int(Rage),
        'blood': Rblood,
        'behavior': Rbehavior,
        'detail': Rdetail,
        'date': datetime.now().strftime('%Y-%m-%d'),
        'doc_resp': ''
    }
    data['users'].append(new_user)
    save_data(data)
    return 'success'
def statistics():
    sql='select count(*) from users'
    cursor.execute(sql)
    total=cursor.fetchone()[0]

    #统计今日新增
    sql = 'select count(*) from users where to_days(date) = to_days(now())'
    cursor.execute(sql)
    new_add=cursor.fetchone()[0]

    res={
        'total':total,
        'new_add':new_add
    }
    return res
def show_patient_info():
    sql="select name,behavior,detail,doc_resp from users"
    cursor.execute(sql)
    res=cursor.fetchall()
    res_list=[]
    for r in res:
        data={
            'name':r[0],
            'behavior':r[1],
            'detail':r[2],
            'doc_resp':r[3]
        }
        res_list.append(data)
    return res_list
def addresp(name,detail,resp):
    sql=f"update users set doc_resp='{resp}' where name='{name}' and detail='{detail}'"
    print(sql)
    cursor.execute(sql)
    db.commit()

def getMypie():
    jjfb_sql="SELECT COUNT(*) from users where behavior='就近方便型'"
    cursor.execute(jjfb_sql)
    jjfb=cursor.fetchone()[0]

    hhgx_sql="SELECT COUNT(*) from users where behavior='合同关系型'"
    cursor.execute(hhgx_sql)
    hhgx = cursor.fetchone()[0]

    rjgx_sql="SELECT COUNT(*) from users where behavior='人际关系型'"
    cursor.execute(rjgx_sql)
    rjgx = cursor.fetchone()[0]

    yyyd_sql ="SELECT COUNT(*) from users where behavior='舆论诱导型'"
    cursor.execute( yyyd_sql)
    yyyd = cursor.fetchone()[0]

    xryl_sql ="SELECT COUNT(*) from users where behavior='信任医疗型'"
    cursor.execute(xryl_sql)
    xryl = cursor.fetchone()[0]

    gyl_sql ="SELECT COUNT(*) from users where behavior='高医疗消费型'"
    cursor.execute(gyl_sql)
    gyl = cursor.fetchone()[0]

    sy_sql ="SELECT COUNT(*) from users where behavior='随意就医型'"
    cursor.execute(sy_sql)
    sy = cursor.fetchone()[0]
    res={
        'jjfb':jjfb,
        'hhgx':hhgx,
        'rjgx':rjgx,
        'yyyd':yyyd,
        'xryl':xryl,
        'gyl':gyl,
        'sy':sy
    }
    return res
def removeUser(name):
    sql="delete from users where name='%s' "%(name)
    print(sql)
    stat=cursor.execute(sql)
    print(stat)
    db.commit()
def searchname(username):
    sql="select * from users where name='%s'"%(username)
    cursor.execute(sql)
    res=cursor.fetchone()
    dict_info={
        'name':res[1],
        'behavior':res[4],
        'detail':res[5],
        'doc_resp':res[7]
    }
    return dict_info
def relations():
    ret_list=[]
    sql="select * from users where detail LIKE '%疼%'"
    cursor.execute(sql)
    res=cursor.fetchall()
    for user in res:
        user_node={"name": user[1], "symbolSize": 20}
        ret_list.append(user_node)
        print(user_node)
    return ret_list









