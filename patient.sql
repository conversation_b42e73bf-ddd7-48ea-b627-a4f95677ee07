/*
 Navicat Premium Data Transfer

 Source Server         : 2023
 Source Server Type    : MySQL
 Source Server Version : 80022
 Source Host           : localhost:3306
 Source Schema         : patient

 Target Server Type    : MySQL
 Target Server Version : 80022
 File Encoding         : 65001

 Date: 03/04/2023 23:41:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `age` int(0) NULL DEFAULT NULL,
  `blood` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `behavior` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `detail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `date` date NULL DEFAULT NULL,
  `doc_resp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, '张三', 23, 'A型血', '就近方便型', '感冒', '2023-03-29', '建议多喝热水');
INSERT INTO `users` VALUES (2, '李四', 23, 'A型血', '人际关系型', '肚子疼', '2023-03-29', '建议少喝凉水');
INSERT INTO `users` VALUES (3, '王五', 34, 'A型血', '人际关系型', '发烧', '2023-03-29', '建议使用退烧贴');
INSERT INTO `users` VALUES (4, '雷雨姐', 12, 'A型血', '就近方便型', '小儿麻痹症', '2023-03-29', '建议吃药治疗');
INSERT INTO `users` VALUES (5, '小雷', 12, 'B型血', '就近方便型', '小儿麻痹症', '2023-03-29', '建议吃药治疗');
INSERT INTO `users` VALUES (6, '李大爷', 77, 'A型血', '就近方便型', '脑袋疼', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (7, '李小姐', 24, 'A型血', '合同关系型', '脚痛', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (8, '人际关系贝总', 22, 'A型血', '人际关系型', '耳朵疼', '2023-03-31', '建议到耳科检查');
INSERT INTO `users` VALUES (9, '舆论诱导小雷', 11, 'O型血', '舆论诱导型', '眼睛疼', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (10, '信任医疗李大妈', 44, 'A型血', '信任医疗型', '眼花缭乱', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (11, '高医疗小王', 20, 'A型血', '高医疗消费型', '脚痛', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (12, '随意就医佛系青年', 12, 'B型血', '随意就医型', '近视', '2023-03-31', '建议去医院配眼镜');
INSERT INTO `users` VALUES (14, '小红', 23, 'A型血', '就近方便型', '感冒', '2023-03-29', '建议多喝热水');
INSERT INTO `users` VALUES (15, '小青', 23, 'A型血', '人际关系型', '肚子疼', '2023-03-29', '建议少喝凉水');
INSERT INTO `users` VALUES (16, '小兰', 34, 'A型血', '人际关系型', '发烧', '2023-03-29', '建议使用退烧贴');
INSERT INTO `users` VALUES (17, '小飞', 12, 'A型血', '就近方便型', '小儿麻痹症', '2023-03-29', '建议吃药治疗');
INSERT INTO `users` VALUES (18, '小菲', 12, 'B型血', '就近方便型', '小儿麻痹症', '2023-03-29', '建议吃药治疗');
INSERT INTO `users` VALUES (19, '小微', 77, 'A型血', '就近方便型', '脑袋疼', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (20, '小伟', 24, 'A型血', '合同关系型', '脚痛', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (21, '小倩', 22, 'A型血', '人际关系型', '耳朵疼', '2023-03-31', '建议到耳科检查');
INSERT INTO `users` VALUES (22, '小娥', 11, 'O型血', '舆论诱导型', '眼睛疼', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (23, '小唐', 44, 'A型血', '信任医疗型', '眼花缭乱', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (24, '大红', 20, 'A型血', '高医疗消费型', '脚痛', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (25, '大飞', 12, 'B型血', '随意就医型', '近视', '2023-03-31', '建议去医院配眼镜');
INSERT INTO `users` VALUES (26, '小平', 22, 'B型血', '就近方便型', '肚子疼', '2023-04-03', '建议多喝热水');
INSERT INTO `users` VALUES (35, '小华', 21, 'O型血', '就近方便型', '肚子疼', '2023-04-03', NULL);

SET FOREIGN_KEY_CHECKS = 1;
