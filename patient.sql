/*
 Navicat Premium Data Transfer

 Source Server         : 2023
 Source Server Type    : MySQL
 Source Server Version : 80022
 Source Host           : localhost:3306
 Source Schema         : patient

 Target Server Type    : MySQL
 Target Server Version : 80022
 File Encoding         : 65001

 Date: 03/04/2023 23:41:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `age` int(0) NULL DEFAULT NULL,
  `blood` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `behavior` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `detail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `date` date NULL DEFAULT NULL,
  `doc_resp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, '张三', 23, 'A型血', '就近方便型', '感冒', '2023-03-29', '建议多喝热水');
INSERT INTO `users` VALUES (2, '李四', 23, 'A型血', '人际关系型', '肚子疼', '2023-03-29', '建议少喝凉水');
INSERT INTO `users` VALUES (3, '王五', 34, 'A型血', '人际关系型', '发烧', '2023-03-29', '建议使用退烧贴');
INSERT INTO `users` VALUES (4, '雷雨姐', 12, 'A型血', '就近方便型', '小儿麻痹症', '2023-03-29', '建议吃药治疗');
INSERT INTO `users` VALUES (5, '小雷', 12, 'B型血', '就近方便型', '小儿麻痹症', '2023-03-29', '建议吃药治疗');
INSERT INTO `users` VALUES (6, '李大爷', 77, 'A型血', '就近方便型', '脑袋疼', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (7, '李小姐', 24, 'A型血', '合同关系型', '脚痛', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (8, '人际关系贝总', 22, 'A型血', '人际关系型', '耳朵疼', '2023-03-31', '建议到耳科检查');
INSERT INTO `users` VALUES (9, '舆论诱导小雷', 11, 'O型血', '舆论诱导型', '眼睛疼', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (10, '信任医疗李大妈', 44, 'A型血', '信任医疗型', '眼花缭乱', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (11, '高医疗小王', 20, 'A型血', '高医疗消费型', '脚痛', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (12, '随意就医佛系青年', 12, 'B型血', '随意就医型', '近视', '2023-03-31', '建议去医院配眼镜');
INSERT INTO `users` VALUES (14, '小红', 23, 'A型血', '就近方便型', '感冒', '2023-03-29', '建议多喝热水');
INSERT INTO `users` VALUES (15, '小青', 23, 'A型血', '人际关系型', '肚子疼', '2023-03-29', '建议少喝凉水');
INSERT INTO `users` VALUES (16, '小兰', 34, 'A型血', '人际关系型', '发烧', '2023-03-29', '建议使用退烧贴');
INSERT INTO `users` VALUES (17, '小飞', 12, 'A型血', '就近方便型', '小儿麻痹症', '2023-03-29', '建议吃药治疗');
INSERT INTO `users` VALUES (18, '小菲', 12, 'B型血', '就近方便型', '小儿麻痹症', '2023-03-29', '建议吃药治疗');
INSERT INTO `users` VALUES (19, '小微', 77, 'A型血', '就近方便型', '脑袋疼', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (20, '小伟', 24, 'A型血', '合同关系型', '脚痛', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (21, '小倩', 22, 'A型血', '人际关系型', '耳朵疼', '2023-03-31', '建议到耳科检查');
INSERT INTO `users` VALUES (22, '小娥', 11, 'O型血', '舆论诱导型', '眼睛疼', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (23, '小唐', 44, 'A型血', '信任医疗型', '眼花缭乱', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (24, '大红', 20, 'A型血', '高医疗消费型', '脚痛', '2023-03-31', '建议多休息');
INSERT INTO `users` VALUES (25, '大飞', 12, 'B型血', '随意就医型', '近视', '2023-03-31', '建议去医院配眼镜');
INSERT INTO `users` VALUES (26, '小平', 22, 'B型血', '就近方便型', '肚子疼', '2023-04-03', '建议多喝热水');
INSERT INTO `users` VALUES (35, '小华', 21, 'O型血', '就近方便型', '肚子疼', '2023-04-03', NULL);

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '科室名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '科室描述',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '科室位置',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '科室电话',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of departments
-- ----------------------------
INSERT INTO `departments` VALUES (1, '内科', '负责内科疾病的诊断和治疗', '1楼东区', '010-12345001', '2023-04-01 08:00:00');
INSERT INTO `departments` VALUES (2, '外科', '负责外科手术和创伤治疗', '2楼西区', '010-12345002', '2023-04-01 08:00:00');
INSERT INTO `departments` VALUES (3, '儿科', '专门治疗儿童疾病', '3楼南区', '010-12345003', '2023-04-01 08:00:00');
INSERT INTO `departments` VALUES (4, '妇产科', '妇科和产科疾病治疗', '4楼北区', '010-12345004', '2023-04-01 08:00:00');
INSERT INTO `departments` VALUES (5, '眼科', '眼部疾病专科治疗', '5楼东区', '010-12345005', '2023-04-01 08:00:00');
INSERT INTO `departments` VALUES (6, '耳鼻喉科', '耳鼻喉疾病专科治疗', '5楼西区', '010-12345006', '2023-04-01 08:00:00');
INSERT INTO `departments` VALUES (7, '骨科', '骨骼和关节疾病治疗', '2楼东区', '010-12345007', '2023-04-01 08:00:00');
INSERT INTO `departments` VALUES (8, '皮肤科', '皮肤疾病专科治疗', '6楼南区', '010-12345008', '2023-04-01 08:00:00');
INSERT INTO `departments` VALUES (9, '神经科', '神经系统疾病治疗', '7楼北区', '010-12345009', '2023-04-01 08:00:00');
INSERT INTO `departments` VALUES (10, '急诊科', '急诊和紧急医疗服务', '1楼中央', '010-12345010', '2023-04-01 08:00:00');

-- ----------------------------
-- Table structure for doctors
-- ----------------------------
DROP TABLE IF EXISTS `doctors`;
CREATE TABLE `doctors`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '医生姓名',
  `gender` enum('男','女') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '性别',
  `age` int(0) NULL DEFAULT NULL COMMENT '年龄',
  `department_id` int(0) UNSIGNED NULL DEFAULT NULL COMMENT '所属科室ID',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '职称',
  `specialization` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '专业特长',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '邮箱',
  `years_experience` int(0) NULL DEFAULT NULL COMMENT '从业年限',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_doctor_department`(`department_id`) USING BTREE,
  CONSTRAINT `fk_doctor_department` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of doctors
-- ----------------------------
INSERT INTO `doctors` VALUES (1, '王建国', '男', 45, 1, '主任医师', '心血管疾病、高血压治疗', '13800138001', '<EMAIL>', 20, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (2, '李美华', '女', 38, 2, '副主任医师', '普外科手术、微创手术', '13800138002', '<EMAIL>', 15, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (3, '张小明', '男', 42, 3, '主治医师', '儿童呼吸道疾病、小儿发育', '13800138003', '<EMAIL>', 18, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (4, '陈丽娟', '女', 40, 4, '主任医师', '妇科肿瘤、产科急救', '13800138004', '<EMAIL>', 17, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (5, '刘志强', '男', 35, 5, '主治医师', '白内障手术、青光眼治疗', '13800138005', '<EMAIL>', 12, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (6, '赵敏', '女', 33, 6, '主治医师', '鼻炎治疗、听力障碍', '13800138006', '<EMAIL>', 10, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (7, '孙大伟', '男', 48, 7, '主任医师', '骨折修复、关节置换', '13800138007', '<EMAIL>', 22, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (8, '周雅琴', '女', 36, 8, '副主任医师', '皮肤过敏、湿疹治疗', '13800138008', '<EMAIL>', 13, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (9, '吴建华', '男', 50, 9, '主任医师', '脑血管疾病、癫痫治疗', '13800138009', '<EMAIL>', 25, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (10, '马丽丽', '女', 29, 10, '住院医师', '急诊抢救、创伤处理', '13800138010', '<EMAIL>', 5, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (11, '胡晓东', '男', 41, 1, '副主任医师', '糖尿病、内分泌疾病', '13800138011', '<EMAIL>', 16, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (12, '林芳芳', '女', 34, 3, '主治医师', '小儿消化系统疾病', '13800138012', '<EMAIL>', 11, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (13, '郭强', '男', 39, 2, '主治医师', '胸外科、心脏手术', '13800138013', '<EMAIL>', 14, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (14, '黄秀英', '女', 44, 4, '副主任医师', '不孕不育、妇科内分泌', '13800138014', '<EMAIL>', 19, '2023-04-01 08:00:00');
INSERT INTO `doctors` VALUES (15, '邓志明', '男', 37, 7, '主治医师', '脊柱外科、运动损伤', '13800138015', '<EMAIL>', 13, '2023-04-01 08:00:00');

-- ----------------------------
-- Table structure for medicines
-- ----------------------------
DROP TABLE IF EXISTS `medicines`;
CREATE TABLE `medicines`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '药品名称',
  `generic_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '通用名',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '药品分类',
  `specification` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '规格',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '单位',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '单价',
  `manufacturer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '生产厂家',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '药品说明',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of medicines
-- ----------------------------
INSERT INTO `medicines` VALUES (1, '阿莫西林胶囊', '阿莫西林', '抗生素', '0.25g*24粒', '盒', 15.80, '华北制药', '用于敏感菌所致的各种感染', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (2, '布洛芬缓释胶囊', '布洛芬', '解热镇痛药', '0.3g*20粒', '盒', 22.50, '中美史克', '用于缓解轻至中度疼痛', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (3, '复方甘草片', '复方甘草', '止咳药', '50片', '瓶', 8.90, '太极集团', '用于镇咳祛痰', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (4, '维生素C片', '维生素C', '维生素类', '0.1g*100片', '瓶', 12.00, '华润三九', '用于预防坏血病', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (5, '感冒灵颗粒', '感冒灵', '感冒药', '10g*9袋', '盒', 18.60, '同仁堂', '用于感冒引起的头痛发热', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (6, '氯雷他定片', '氯雷他定', '抗过敏药', '10mg*7片', '盒', 25.30, '拜耳', '用于缓解过敏性鼻炎症状', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (7, '奥美拉唑肠溶胶囊', '奥美拉唑', '胃药', '20mg*14粒', '盒', 35.70, '阿斯利康', '用于胃溃疡、十二指肠溃疡', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (8, '硝苯地平缓释片', '硝苯地平', '降压药', '20mg*30片', '盒', 42.80, '拜耳', '用于高血压、心绞痛', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (9, '二甲双胍片', '二甲双胍', '降糖药', '0.25g*48片', '盒', 28.90, '中美上海施贵宝', '用于2型糖尿病', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (10, '阿司匹林肠溶片', '阿司匹林', '抗血小板药', '25mg*30片', '盒', 16.50, '拜耳', '用于预防心脑血管疾病', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (11, '头孢克肟胶囊', '头孢克肟', '抗生素', '0.1g*6粒', '盒', 38.20, '齐鲁制药', '用于敏感细菌感染', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (12, '蒙脱石散', '蒙脱石', '止泻药', '3g*10袋', '盒', 19.80, '博福-益普生', '用于急慢性腹泻', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (13, '复方丹参滴丸', '复方丹参', '心血管药', '270丸', '瓶', 32.60, '天士力', '用于胸闷心痛', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (14, '金银花颗粒', '金银花', '清热解毒药', '10g*10袋', '盒', 14.70, '康恩贝', '用于风热感冒', '2023-04-01 08:00:00');
INSERT INTO `medicines` VALUES (15, '开塞露', '甘油', '通便药', '20ml*10支', '盒', 9.50, '山东方明', '用于便秘', '2023-04-01 08:00:00');

-- ----------------------------
-- Table structure for medical_records
-- ----------------------------
DROP TABLE IF EXISTS `medical_records`;
CREATE TABLE `medical_records`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `patient_id` int(0) UNSIGNED NOT NULL COMMENT '患者ID',
  `doctor_id` int(0) UNSIGNED NOT NULL COMMENT '医生ID',
  `visit_date` datetime NOT NULL COMMENT '就诊日期',
  `chief_complaint` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '主诉',
  `present_illness` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '现病史',
  `past_history` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '既往史',
  `physical_examination` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '体格检查',
  `diagnosis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '诊断',
  `treatment_plan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '治疗方案',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_record_patient`(`patient_id`) USING BTREE,
  INDEX `fk_record_doctor`(`doctor_id`) USING BTREE,
  CONSTRAINT `fk_record_patient` FOREIGN KEY (`patient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_record_doctor` FOREIGN KEY (`doctor_id`) REFERENCES `doctors` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of medical_records
-- ----------------------------
INSERT INTO `medical_records` VALUES (1, 1, 1, '2023-03-29 09:30:00', '感冒症状', '患者3天前开始出现鼻塞、流涕、咳嗽', '既往体健', '体温37.2℃，咽部充血', '上呼吸道感染', '多休息，多饮水，对症治疗', '建议多喝热水', '2023-03-29 09:30:00');
INSERT INTO `medical_records` VALUES (2, 2, 11, '2023-03-29 10:15:00', '腹痛', '患者昨晚开始腹痛，伴有恶心', '无特殊病史', '腹部轻压痛，无反跳痛', '急性胃炎', '禁食，静脉补液', '建议少喝凉水', '2023-03-29 10:15:00');
INSERT INTO `medical_records` VALUES (3, 3, 1, '2023-03-29 14:20:00', '发热', '患者2天前开始发热，最高39℃', '既往体健', '体温38.8℃，精神可', '病毒性感冒', '退热治疗，抗病毒', '建议使用退烧贴', '2023-03-29 14:20:00');
INSERT INTO `medical_records` VALUES (4, 4, 3, '2023-03-29 16:45:00', '肢体无力', '患儿出生后发现肢体发育异常', '早产儿', '肌张力低下，反射减弱', '小儿麻痹症', '康复训练，营养支持', '建议吃药治疗', '2023-03-29 16:45:00');
INSERT INTO `medical_records` VALUES (5, 5, 3, '2023-03-29 17:30:00', '肢体无力', '患儿出生后发现肢体发育异常', '足月儿', '肌张力低下，反射减弱', '小儿麻痹症', '康复训练，营养支持', '建议吃药治疗', '2023-03-29 17:30:00');
INSERT INTO `medical_records` VALUES (6, 6, 9, '2023-03-31 08:30:00', '头痛', '患者近1周头痛加重', '高血压病史10年', '血压160/95mmHg', '高血压性头痛', '降压治疗，生活方式干预', '建议多休息', '2023-03-31 08:30:00');
INSERT INTO `medical_records` VALUES (7, 7, 7, '2023-03-31 11:20:00', '足部疼痛', '患者走路时足部疼痛1周', '无外伤史', '足部无明显肿胀，压痛明显', '足底筋膜炎', '休息，理疗，消炎镇痛', '建议多休息', '2023-03-31 11:20:00');
INSERT INTO `medical_records` VALUES (8, 8, 6, '2023-03-31 15:10:00', '耳痛', '患者右耳疼痛2天', '既往体健', '右耳道充血，鼓膜充血', '急性中耳炎', '抗生素治疗，局部用药', '建议到耳科检查', '2023-03-31 15:10:00');
INSERT INTO `medical_records` VALUES (9, 9, 5, '2023-03-31 16:40:00', '眼痛', '患者双眼疼痛，视物模糊', '近视史', '双眼结膜充血，视力下降', '干眼症', '人工泪液，注意用眼卫生', '建议多休息', '2023-03-31 16:40:00');
INSERT INTO `medical_records` VALUES (10, 10, 5, '2023-03-31 17:25:00', '视物模糊', '患者近期视物模糊，眼花', '糖尿病史5年', '眼底检查异常', '糖尿病视网膜病变', '控制血糖，眼科治疗', '建议多休息', '2023-03-31 17:25:00');
INSERT INTO `medical_records` VALUES (11, 11, 7, '2023-03-31 18:00:00', '足部疼痛', '患者运动后足部疼痛', '既往体健', '足部轻度肿胀', '运动损伤', '休息，冰敷，消炎镇痛', '建议多休息', '2023-03-31 18:00:00');
INSERT INTO `medical_records` VALUES (12, 12, 5, '2023-03-31 19:15:00', '视力下降', '患者近期视力明显下降', '既往体健', '双眼屈光不正', '近视', '配镜矫正', '建议去医院配眼镜', '2023-03-31 19:15:00');
INSERT INTO `medical_records` VALUES (13, 26, 1, '2023-04-03 09:00:00', '腹痛', '患者昨晚开始腹痛', '既往体健', '腹部压痛', '急性胃炎', '对症治疗', '建议多喝热水', '2023-04-03 09:00:00');
INSERT INTO `medical_records` VALUES (14, 35, 1, '2023-04-03 10:30:00', '腹痛', '患者腹痛半天', '既往体健', '腹部轻压痛', '消化不良', '饮食调理，对症治疗', '待进一步观察', '2023-04-03 10:30:00');
INSERT INTO `medical_records` VALUES (15, 14, 1, '2023-04-04 14:20:00', '感冒复查', '感冒症状好转', '既往体健', '体温正常，咽部好转', '上呼吸道感染恢复期', '继续观察', '恢复良好', '2023-04-04 14:20:00');

-- ----------------------------
-- Table structure for prescriptions
-- ----------------------------
DROP TABLE IF EXISTS `prescriptions`;
CREATE TABLE `prescriptions`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `medical_record_id` int(0) UNSIGNED NOT NULL COMMENT '病历ID',
  `medicine_id` int(0) UNSIGNED NOT NULL COMMENT '药品ID',
  `quantity` int(0) NOT NULL COMMENT '数量',
  `dosage` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用法用量',
  `duration` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '用药时长',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_prescription_record`(`medical_record_id`) USING BTREE,
  INDEX `fk_prescription_medicine`(`medicine_id`) USING BTREE,
  CONSTRAINT `fk_prescription_record` FOREIGN KEY (`medical_record_id`) REFERENCES `medical_records` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_prescription_medicine` FOREIGN KEY (`medicine_id`) REFERENCES `medicines` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of prescriptions
-- ----------------------------
INSERT INTO `prescriptions` VALUES (1, 1, 5, 1, '每次1袋，每日3次，饭后服用', '3天', '多喝水', '2023-03-29 09:30:00');
INSERT INTO `prescriptions` VALUES (2, 1, 4, 1, '每次2片，每日2次', '7天', '增强免疫力', '2023-03-29 09:30:00');
INSERT INTO `prescriptions` VALUES (3, 2, 7, 1, '每次1粒，每日2次，饭前服用', '7天', '保护胃黏膜', '2023-03-29 10:15:00');
INSERT INTO `prescriptions` VALUES (4, 3, 2, 1, '每次1粒，每日2次，发热时服用', '3天', '退热镇痛', '2023-03-29 14:20:00');
INSERT INTO `prescriptions` VALUES (5, 3, 14, 1, '每次1袋，每日3次', '5天', '清热解毒', '2023-03-29 14:20:00');
INSERT INTO `prescriptions` VALUES (6, 4, 4, 2, '每次2片，每日3次', '30天', '营养支持', '2023-03-29 16:45:00');
INSERT INTO `prescriptions` VALUES (7, 5, 4, 2, '每次2片，每日3次', '30天', '营养支持', '2023-03-29 17:30:00');
INSERT INTO `prescriptions` VALUES (8, 6, 8, 1, '每次1片，每日1次，晨起服用', '30天', '控制血压', '2023-03-31 08:30:00');
INSERT INTO `prescriptions` VALUES (9, 7, 2, 1, '每次1粒，每日2次，疼痛时服用', '7天', '消炎镇痛', '2023-03-31 11:20:00');
INSERT INTO `prescriptions` VALUES (10, 8, 1, 1, '每次2粒，每日3次，饭后服用', '7天', '抗感染治疗', '2023-03-31 15:10:00');
INSERT INTO `prescriptions` VALUES (11, 9, 6, 1, '每次1片，每日1次', '14天', '抗过敏治疗', '2023-03-31 16:40:00');
INSERT INTO `prescriptions` VALUES (12, 10, 9, 1, '每次2片，每日2次，饭后服用', '30天', '控制血糖', '2023-03-31 17:25:00');
INSERT INTO `prescriptions` VALUES (13, 11, 2, 1, '每次1粒，每日2次，疼痛时服用', '5天', '消炎镇痛', '2023-03-31 18:00:00');
INSERT INTO `prescriptions` VALUES (14, 13, 7, 1, '每次1粒，每日2次，饭前服用', '7天', '保护胃黏膜', '2023-04-03 09:00:00');
INSERT INTO `prescriptions` VALUES (15, 14, 12, 1, '每次1袋，每日3次，腹泻时服用', '3天', '止泻治疗', '2023-04-03 10:30:00');
INSERT INTO `prescriptions` VALUES (16, 1, 3, 1, '每次2片，每日3次，咳嗽时服用', '5天', '止咳化痰', '2023-03-29 09:30:00');
INSERT INTO `prescriptions` VALUES (17, 6, 13, 1, '每次10粒，每日3次', '14天', '活血化瘀', '2023-03-31 08:30:00');
INSERT INTO `prescriptions` VALUES (18, 8, 11, 1, '每次1粒，每日2次，饭后服用', '5天', '抗感染治疗', '2023-03-31 15:10:00');
INSERT INTO `prescriptions` VALUES (19, 2, 12, 1, '每次1袋，每日3次，腹泻时服用', '3天', '止泻保护', '2023-03-29 10:15:00');
INSERT INTO `prescriptions` VALUES (20, 10, 10, 1, '每次1片，每日1次，晨起服用', '长期', '预防血栓', '2023-03-31 17:25:00');

-- ----------------------------
-- Table structure for appointments
-- ----------------------------
DROP TABLE IF EXISTS `appointments`;
CREATE TABLE `appointments`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `patient_id` int(0) UNSIGNED NOT NULL COMMENT '患者ID',
  `doctor_id` int(0) UNSIGNED NOT NULL COMMENT '医生ID',
  `appointment_date` datetime NOT NULL COMMENT '预约日期时间',
  `status` enum('已预约','已完成','已取消','爽约') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '已预约' COMMENT '预约状态',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '预约原因',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_appointment_patient`(`patient_id`) USING BTREE,
  INDEX `fk_appointment_doctor`(`doctor_id`) USING BTREE,
  CONSTRAINT `fk_appointment_patient` FOREIGN KEY (`patient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_appointment_doctor` FOREIGN KEY (`doctor_id`) REFERENCES `doctors` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of appointments
-- ----------------------------
INSERT INTO `appointments` VALUES (1, 1, 1, '2023-04-05 09:00:00', '已预约', '感冒复查', '需要复查感冒恢复情况', '2023-04-04 10:00:00');
INSERT INTO `appointments` VALUES (2, 2, 11, '2023-04-06 10:30:00', '已预约', '胃痛复查', '胃炎治疗后复查', '2023-04-04 11:00:00');
INSERT INTO `appointments` VALUES (3, 6, 9, '2023-04-07 08:30:00', '已预约', '血压复查', '高血压用药调整', '2023-04-04 12:00:00');
INSERT INTO `appointments` VALUES (4, 10, 5, '2023-04-08 14:00:00', '已预约', '眼底检查', '糖尿病眼底病变复查', '2023-04-04 13:00:00');
INSERT INTO `appointments` VALUES (5, 12, 5, '2023-04-09 15:30:00', '已预约', '配镜复查', '近视配镜后复查', '2023-04-04 14:00:00');
INSERT INTO `appointments` VALUES (6, 15, 1, '2023-04-10 09:30:00', '已预约', '体检', '年度健康体检', '2023-04-04 15:00:00');
INSERT INTO `appointments` VALUES (7, 16, 1, '2023-04-11 10:00:00', '已预约', '发热', '持续发热需要检查', '2023-04-04 16:00:00');
INSERT INTO `appointments` VALUES (8, 20, 7, '2023-04-12 11:00:00', '已预约', '脚痛复查', '足部疼痛治疗后复查', '2023-04-04 17:00:00');
INSERT INTO `appointments` VALUES (9, 21, 6, '2023-04-13 14:30:00', '已预约', '耳痛复查', '中耳炎治疗后复查', '2023-04-04 18:00:00');
INSERT INTO `appointments` VALUES (10, 23, 5, '2023-04-14 16:00:00', '已预约', '眼科检查', '眼花症状检查', '2023-04-04 19:00:00');
INSERT INTO `appointments` VALUES (11, 35, 1, '2023-04-15 09:00:00', '已预约', '腹痛复查', '消化不良复查', '2023-04-04 20:00:00');
INSERT INTO `appointments` VALUES (12, 26, 11, '2023-04-16 10:30:00', '已预约', '胃部检查', '胃炎后续治疗', '2023-04-04 21:00:00');
INSERT INTO `appointments` VALUES (13, 4, 3, '2023-04-17 15:00:00', '已预约', '康复训练', '小儿麻痹症康复评估', '2023-04-04 22:00:00');
INSERT INTO `appointments` VALUES (14, 5, 3, '2023-04-18 16:30:00', '已预约', '康复训练', '小儿麻痹症康复评估', '2023-04-04 23:00:00');
INSERT INTO `appointments` VALUES (15, 8, 6, '2023-04-19 11:30:00', '已预约', '听力检查', '中耳炎后听力评估', '2023-04-05 08:00:00');

-- ----------------------------
-- Table structure for examinations
-- ----------------------------
DROP TABLE IF EXISTS `examinations`;
CREATE TABLE `examinations`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `medical_record_id` int(0) UNSIGNED NOT NULL COMMENT '病历ID',
  `exam_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '检查类型',
  `exam_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '检查项目名称',
  `exam_date` datetime NOT NULL COMMENT '检查日期',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '检查结果',
  `normal_range` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '正常范围',
  `status` enum('待检查','已完成','异常') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '待检查' COMMENT '检查状态',
  `cost` decimal(10, 2) NULL DEFAULT NULL COMMENT '检查费用',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_exam_record`(`medical_record_id`) USING BTREE,
  CONSTRAINT `fk_exam_record` FOREIGN KEY (`medical_record_id`) REFERENCES `medical_records` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of examinations
-- ----------------------------
INSERT INTO `examinations` VALUES (1, 1, '血常规', '血常规检查', '2023-03-29 09:45:00', '白细胞计数略高', '4.0-10.0×10^9/L', '异常', 25.00, '提示轻度感染', '2023-03-29 09:45:00');
INSERT INTO `examinations` VALUES (2, 2, '腹部B超', '腹部超声检查', '2023-03-29 10:30:00', '胃壁增厚', '正常', '异常', 120.00, '符合胃炎表现', '2023-03-29 10:30:00');
INSERT INTO `examinations` VALUES (3, 3, '血常规', '血常规检查', '2023-03-29 14:35:00', '白细胞计数正常', '4.0-10.0×10^9/L', '已完成', 25.00, '病毒感染可能', '2023-03-29 14:35:00');
INSERT INTO `examinations` VALUES (4, 6, '心电图', '12导联心电图', '2023-03-31 08:45:00', '左心室肥厚', '正常', '异常', 50.00, '高血压心脏改变', '2023-03-31 08:45:00');
INSERT INTO `examinations` VALUES (5, 6, '血压监测', '24小时动态血压', '2023-03-31 09:00:00', '平均血压155/92mmHg', '<140/90mmHg', '异常', 200.00, '血压控制不佳', '2023-03-31 09:00:00');
INSERT INTO `examinations` VALUES (6, 7, 'X光片', '足部X光检查', '2023-03-31 11:35:00', '骨质结构正常', '正常', '已完成', 80.00, '排除骨折', '2023-03-31 11:35:00');
INSERT INTO `examinations` VALUES (7, 8, '耳镜检查', '耳部内镜检查', '2023-03-31 15:25:00', '鼓膜充血，有积液', '正常', '异常', 60.00, '急性中耳炎表现', '2023-03-31 15:25:00');
INSERT INTO `examinations` VALUES (8, 9, '眼压检查', '眼压测量', '2023-03-31 16:55:00', '双眼眼压正常', '10-21mmHg', '已完成', 40.00, '排除青光眼', '2023-03-31 16:55:00');
INSERT INTO `examinations` VALUES (9, 10, '眼底检查', '眼底镜检查', '2023-03-31 17:40:00', '视网膜出血点', '正常', '异常', 100.00, '糖尿病视网膜病变', '2023-03-31 17:40:00');
INSERT INTO `examinations` VALUES (10, 10, '血糖检查', '空腹血糖', '2023-03-31 17:45:00', '8.5mmol/L', '3.9-6.1mmol/L', '异常', 15.00, '血糖控制不佳', '2023-03-31 17:45:00');
INSERT INTO `examinations` VALUES (11, 12, '视力检查', '视力表检查', '2023-03-31 19:30:00', '右眼0.3，左眼0.4', '1.0', '异常', 20.00, '中度近视', '2023-03-31 19:30:00');
INSERT INTO `examinations` VALUES (12, 13, '血常规', '血常规检查', '2023-04-03 09:15:00', '各项指标正常', '正常范围内', '已完成', 25.00, '无异常', '2023-04-03 09:15:00');
INSERT INTO `examinations` VALUES (13, 14, '腹部B超', '腹部超声检查', '2023-04-03 10:45:00', '各脏器形态正常', '正常', '已完成', 120.00, '无明显异常', '2023-04-03 10:45:00');
INSERT INTO `examinations` VALUES (14, 1, '胸部X光', '胸部正位片', '2023-03-29 10:00:00', '双肺纹理清晰', '正常', '已完成', 60.00, '排除肺炎', '2023-03-29 10:00:00');
INSERT INTO `examinations` VALUES (15, 8, '听力检查', '纯音听阈测试', '2023-03-31 15:40:00', '轻度传导性听力下降', '正常', '异常', 150.00, '中耳炎影响听力', '2023-03-31 15:40:00');

-- ----------------------------
-- Table structure for billing
-- ----------------------------
DROP TABLE IF EXISTS `billing`;
CREATE TABLE `billing`  (
  `id` int(0) UNSIGNED NOT NULL AUTO_INCREMENT,
  `patient_id` int(0) UNSIGNED NOT NULL COMMENT '患者ID',
  `medical_record_id` int(0) UNSIGNED NULL DEFAULT NULL COMMENT '病历ID',
  `bill_date` datetime NOT NULL COMMENT '账单日期',
  `item_type` enum('挂号费','诊疗费','药品费','检查费','治疗费','其他') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '费用类型',
  `item_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '费用项目',
  `quantity` int(0) DEFAULT 1 COMMENT '数量',
  `unit_price` decimal(10, 2) NOT NULL COMMENT '单价',
  `total_amount` decimal(10, 2) NOT NULL COMMENT '总金额',
  `payment_status` enum('未支付','已支付','部分支付','已退款') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '未支付' COMMENT '支付状态',
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '支付方式',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_billing_patient`(`patient_id`) USING BTREE,
  INDEX `fk_billing_record`(`medical_record_id`) USING BTREE,
  CONSTRAINT `fk_billing_patient` FOREIGN KEY (`patient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_billing_record` FOREIGN KEY (`medical_record_id`) REFERENCES `medical_records` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of billing
-- ----------------------------
INSERT INTO `billing` VALUES (1, 1, 1, '2023-03-29 09:30:00', '挂号费', '内科普通号', 1, 10.00, 10.00, '已支付', '微信支付', '感冒就诊', '2023-03-29 09:30:00');
INSERT INTO `billing` VALUES (2, 1, 1, '2023-03-29 09:30:00', '诊疗费', '内科诊疗费', 1, 20.00, 20.00, '已支付', '微信支付', '医生诊疗', '2023-03-29 09:30:00');
INSERT INTO `billing` VALUES (3, 1, 1, '2023-03-29 09:30:00', '药品费', '感冒灵颗粒', 1, 18.60, 18.60, '已支付', '微信支付', '感冒用药', '2023-03-29 09:30:00');
INSERT INTO `billing` VALUES (4, 1, 1, '2023-03-29 09:30:00', '药品费', '维生素C片', 1, 12.00, 12.00, '已支付', '微信支付', '增强免疫', '2023-03-29 09:30:00');
INSERT INTO `billing` VALUES (5, 1, 1, '2023-03-29 09:45:00', '检查费', '血常规检查', 1, 25.00, 25.00, '已支付', '微信支付', '血液检查', '2023-03-29 09:45:00');
INSERT INTO `billing` VALUES (6, 2, 2, '2023-03-29 10:15:00', '挂号费', '内科普通号', 1, 10.00, 10.00, '已支付', '支付宝', '腹痛就诊', '2023-03-29 10:15:00');
INSERT INTO `billing` VALUES (7, 2, 2, '2023-03-29 10:15:00', '诊疗费', '内科诊疗费', 1, 20.00, 20.00, '已支付', '支付宝', '医生诊疗', '2023-03-29 10:15:00');
INSERT INTO `billing` VALUES (8, 2, 2, '2023-03-29 10:15:00', '药品费', '奥美拉唑肠溶胶囊', 1, 35.70, 35.70, '已支付', '支付宝', '胃药', '2023-03-29 10:15:00');
INSERT INTO `billing` VALUES (9, 2, 2, '2023-03-29 10:30:00', '检查费', '腹部B超', 1, 120.00, 120.00, '已支付', '支付宝', '腹部检查', '2023-03-29 10:30:00');
INSERT INTO `billing` VALUES (10, 3, 3, '2023-03-29 14:20:00', '挂号费', '内科普通号', 1, 10.00, 10.00, '已支付', '现金', '发热就诊', '2023-03-29 14:20:00');
INSERT INTO `billing` VALUES (11, 3, 3, '2023-03-29 14:20:00', '诊疗费', '内科诊疗费', 1, 20.00, 20.00, '已支付', '现金', '医生诊疗', '2023-03-29 14:20:00');
INSERT INTO `billing` VALUES (12, 3, 3, '2023-03-29 14:20:00', '药品费', '布洛芬缓释胶囊', 1, 22.50, 22.50, '已支付', '现金', '退热药', '2023-03-29 14:20:00');
INSERT INTO `billing` VALUES (13, 6, 6, '2023-03-31 08:30:00', '挂号费', '神经科专家号', 1, 50.00, 50.00, '已支付', '医保卡', '头痛就诊', '2023-03-31 08:30:00');
INSERT INTO `billing` VALUES (14, 6, 6, '2023-03-31 08:30:00', '诊疗费', '专家诊疗费', 1, 80.00, 80.00, '已支付', '医保卡', '专家诊疗', '2023-03-31 08:30:00');
INSERT INTO `billing` VALUES (15, 6, 6, '2023-03-31 08:30:00', '药品费', '硝苯地平缓释片', 1, 42.80, 42.80, '已支付', '医保卡', '降压药', '2023-03-31 08:30:00');
INSERT INTO `billing` VALUES (16, 7, 7, '2023-03-31 11:20:00', '挂号费', '骨科普通号', 1, 10.00, 10.00, '已支付', '微信支付', '足痛就诊', '2023-03-31 11:20:00');
INSERT INTO `billing` VALUES (17, 8, 8, '2023-03-31 15:10:00', '挂号费', '耳鼻喉科普通号', 1, 10.00, 10.00, '已支付', '支付宝', '耳痛就诊', '2023-03-31 15:10:00');
INSERT INTO `billing` VALUES (18, 8, 8, '2023-03-31 15:10:00', '药品费', '阿莫西林胶囊', 1, 15.80, 15.80, '已支付', '支付宝', '抗生素', '2023-03-31 15:10:00');
INSERT INTO `billing` VALUES (19, 9, 9, '2023-03-31 16:40:00', '挂号费', '眼科普通号', 1, 10.00, 10.00, '已支付', '现金', '眼痛就诊', '2023-03-31 16:40:00');
INSERT INTO `billing` VALUES (20, 10, 10, '2023-03-31 17:25:00', '挂号费', '眼科专家号', 1, 50.00, 50.00, '已支付', '医保卡', '眼底检查', '2023-03-31 17:25:00');
INSERT INTO `billing` VALUES (21, 10, 10, '2023-03-31 17:25:00', '检查费', '眼底检查', 1, 100.00, 100.00, '已支付', '医保卡', '糖尿病眼底', '2023-03-31 17:25:00');
INSERT INTO `billing` VALUES (22, 12, 12, '2023-03-31 19:15:00', '挂号费', '眼科普通号', 1, 10.00, 10.00, '已支付', '微信支付', '配镜检查', '2023-03-31 19:15:00');
INSERT INTO `billing` VALUES (23, 12, 12, '2023-03-31 19:15:00', '检查费', '视力检查', 1, 20.00, 20.00, '已支付', '微信支付', '视力测试', '2023-03-31 19:15:00');
INSERT INTO `billing` VALUES (24, 26, 13, '2023-04-03 09:00:00', '挂号费', '内科普通号', 1, 10.00, 10.00, '已支付', '支付宝', '腹痛就诊', '2023-04-03 09:00:00');
INSERT INTO `billing` VALUES (25, 35, 14, '2023-04-03 10:30:00', '挂号费', '内科普通号', 1, 10.00, 10.00, '未支付', NULL, '腹痛就诊', '2023-04-03 10:30:00');
INSERT INTO `billing` VALUES (26, 35, 14, '2023-04-03 10:30:00', '检查费', '腹部B超', 1, 120.00, 120.00, '未支付', NULL, '腹部检查', '2023-04-03 10:30:00');
INSERT INTO `billing` VALUES (27, 1, 1, '2023-03-29 10:00:00', '检查费', '胸部X光', 1, 60.00, 60.00, '已支付', '微信支付', '胸部检查', '2023-03-29 10:00:00');
INSERT INTO `billing` VALUES (28, 8, 8, '2023-03-31 15:40:00', '检查费', '听力检查', 1, 150.00, 150.00, '已支付', '支付宝', '听力测试', '2023-03-31 15:40:00');
INSERT INTO `billing` VALUES (29, 6, 6, '2023-03-31 08:45:00', '检查费', '心电图', 1, 50.00, 50.00, '已支付', '医保卡', '心电图检查', '2023-03-31 08:45:00');
INSERT INTO `billing` VALUES (30, 6, 6, '2023-03-31 09:00:00', '检查费', '24小时动态血压', 1, 200.00, 200.00, '已支付', '医保卡', '血压监测', '2023-03-31 09:00:00');

SET FOREIGN_KEY_CHECKS = 1;
