<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Mobile first web app theme | first</title>
  <meta name="description" content="mobile first, app, web app, responsive, admin dashboard, flat, flat ui">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"> 
  <link rel="stylesheet" href="css/bootstrap.css">
  <link rel="stylesheet" href="css/font-awesome.min.css">
  <link rel="stylesheet" href="css/style.css">
  <!--[if lt IE 9]>
    <script src="js/ie/respond.min.js"></script>
    <script src="js/ie/html5.js"></script>
  <![endif]-->
</head>
<body>
  <!-- header -->
  <header id="header" class="navbar">    
    <ul class="nav navbar-nav navbar-avatar pull-right">
      <li class="dropdown">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown">            
          <span class="hidden-sm-only">Mika <PERSON></span>
          <span class="thumb-small avatar inline"><img src="images/avatar.jpg" alt="Mika Sokeil" class="img-circle"></span>
          <b class="caret hidden-sm-only"></b>
        </a>
        <ul class="dropdown-menu">
          <li><a href="#">Settings</a></li>
          <li><a href="#">Profile</a></li>
          <li><a href="#"><span class="badge bg-danger pull-right">3</span>Notifications</a></li>
          <li class="divider"></li>
          <li><a href="docs.html">Help</a></li>
          <li><a href="signin.html">Logout</a></li>
        </ul>
      </li>
    </ul>
    <a class="navbar-brand" href="#">first</a>
    <button type="button" class="btn btn-link pull-left nav-toggle hidden-lg" data-toggle="class:slide-nav slide-nav-right" data-target="body">
      <i class="icon-reorder icon-xlarge text-default"></i>
    </button>
    <ul class="nav navbar-nav hidden-sm">
      <li>
        <div class="m-t m-b-small" id="panel-notifications">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="icon-comment-alt icon-xlarge text-default"></i><b class="badge badge-notes bg-danger count-n">2</b></a>
          <section class="dropdown-menu m-l-small m-t-mini">
            <section class="panel panel-large arrow arrow-top">
              <header class="panel-heading bg-white"><span class="p"><strong>You have <span class="count-n">2</span> notifications</strong></span></header>
              <div class="list-group list-group-flush m-t-n">
                <a href="#" class="media list-group-item">
                  <span class="pull-left thumb-small"><img src="images/avatar.jpg" alt="John said" class="img-circle"></span>
                  <span class="media-body block m-b-none">
                    Moved to Bootstrap 3.0<br>
                    <small class="text-muted">23 June 13</small>
                  </span>
                </a>
                <a href="#" class="media list-group-item">
                  <span class="media-body block m-b-none">
                    first v.1 (Bootstrap 2.3 based) released<br>
                    <small class="text-muted">19 June 13</small>
                  </span>
                </a>
              </div>
              <footer class="panel-footer text-small">
                <a href="#" class="pull-right"><i class="icon-cog"></i></a>
                <a href="#">See all the notifications</a>
              </footer>
            </section>
          </section>
        </div>
      </li>
      <li><div class="m-t-small"><a class="btn btn-small btn-info" data-toggle="modal" href="#modal"><i class="icon-plus"></i> POST</a></div></li>
      <li class="dropdown shift" data-toggle="shift:appendTo" data-target=".nav-primary .nav">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="icon-cog icon-xlarge visible-sm visible-sm-inline"></i>Settings <b class="caret hidden-sm-only"></b></a>
        <ul class="dropdown-menu">
          <li>
            <a href="#" data-toggle="class:navbar-fixed" data-target='body'>Navbar 
              <span class="text-active">auto</span>
              <span class="text">fixed</span>
            </a>
          </li>
          <li class="visible-lg">
            <a href="#" data-toggle="class:nav-vertical" data-target="#nav">Nav 
              <span class="text-active">vertical</span>
              <span class="text">horizontal</span>
            </a>
          </li>
          <li class="divider hidden-sm"></li>
          <li class="dropdown-header">Colors</li>
          <li>
            <a href="#" data-toggle="class:bg bg-black" data-target='.navbar'>Navbar 
              <span class="text-active">white</span>
              <span class="text">inverse</span>
            </a>
          </li>
          <li>
            <a href="#" data-toggle="class:bg-light" data-target='#nav'>Nav 
              <span class="text-active">inverse</span>
              <span class="text">light</span>
            </a>
          </li>
        </ul>
      </li>
    </ul>
    <form class="navbar-form pull-left shift" action="" data-toggle="shift:appendTo" data-target=".nav-primary">
      <i class="icon-search text-muted"></i>
      <input type="text" class="input-small form-control" placeholder="Search">
    </form>
  </header>
  <!-- / header -->
  <!-- nav -->
  <nav id="nav" class="nav-primary visible-lg nav-vertical">
    <ul class="nav" data-spy="affix" data-offset-top="50">
      <li><a href="index.html"><i class="icon-dashboard icon-xlarge"></i>Dashboard</a></li>
      <li class="dropdown-submenu active">
        <a href="#"><i class="icon-th icon-xlarge"></i>Elements</a>
        <ul class="dropdown-menu">
          <li><a href="buttons.html">Buttons</a></li>
          <li><a href="icons.html"><b class="badge pull-right">302</b>Icons</a></li>            
          <li><a href="grid.html">Grid</a></li>
          <li><a href="widgets.html"><b class="badge bg-primary pull-right">8</b>Widgets</a></li>
          <li><a href="components.html"><b class="badge pull-right">18</b>Components</a></li>
        </ul>
      </li>
      <li class="dropdown-submenu">
        <a href="#"><i class="icon-list icon-xlarge"></i>Lists</a>
        <ul class="dropdown-menu">
          <li><a href="list.html">List groups</a></li>
          <li><a href="table.html">Table</a></li>
        </ul>
      </li>
      <li><a href="form.html"><i class="icon-edit icon-xlarge"></i>Form</a></li>
      <li><a href="chart.html"><i class="icon-signal icon-xlarge"></i>Chart</a></li>
      <li class="dropdown-submenu">
        <a href="#"><i class="icon-link icon-xlarge"></i>Others</a>
        <ul class="dropdown-menu">
          <li><a href="signin.html">Signin page</a></li>
          <li><a href="signup.html">Signup page</a></li>
          <li><a href="404.html">404 page</a></li>
        </ul>
      </li>
    </ul>
  </nav>
  <!-- / nav -->
  <section id="content">
    <section class="main padder">
      <div class="clearfix">
        <h4><i class="icon-hand-up"></i>Buttons</h4>
      </div>
      <div class="row">
        <div class="col-lg-6">
          <section class="panel">
            <header class="panel-heading bg-white">Button options</header>
            <div class="doc-buttons">
              <a href="#" class="btn btn-default">Default</a>
              <a href="#" class="btn btn-white">White</a>
              <a href="#" class="btn btn-primary">Primary</a>
              <a href="#" class="btn btn-success">Success</a>
              <a href="#" class="btn btn-info">Info</a>
              <a href="#" class="btn btn-inverse">Inverse</a>
              <a href="#" class="btn btn-warning">Warning</a>
              <a href="#" class="btn btn-danger">Danger</a>
              <a href="#" class="btn btn-danger disabled">Disabled</a>
            </div>
          </section>          
          <section class="panel">
            <header class="panel-heading">Button size</header>
            <p>
              <a href="#" class="btn btn-white btn-large">Large button</a>
              <a href="#" class="btn btn-default btn-large">Button</a>
            </p>
            <p>
              <a href="#" class="btn btn-white">Default button</a>
              <a href="#" class="btn btn-default">Button</a>
            </p>
            <p>
              <a href="#" class="btn btn-white btn-small">Small button</a>
              <a href="#" class="btn btn-default btn-small">Button</a>
            </p>
            <p>
              <a href="#" class="btn btn-white btn-mini">Mini button</a>
              <a href="#" class="btn btn-default btn-mini">Button</a>
            </p>
          </section>          
          <section class="panel">
            <header class="panel-heading">Button dropdowns</header>
            <p>Single button dropdowns</p>
            <div class="block">
              <div class="btn-group">
                <button class="btn btn-white dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">Action</a></li>
                  <li><a href="#">Another action</a></li>
                  <li><a href="#">Something else here</a></li>
                  <li class="divider"></li>
                  <li><a href="#">Separated link</a></li>
                </ul>
              </div>
              <div class="btn-group">
                <button class="btn btn-primary dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">Action</a></li>
                  <li><a href="#">Another action</a></li>
                  <li><a href="#">Something else here</a></li>
                  <li class="divider"></li>
                  <li><a href="#">Separated link</a></li>
                </ul>
              </div>
            </div>
            <div class="block">
              <div class="btn-group">
                <button class="btn btn-white btn-small dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">Action</a></li>
                  <li><a href="#">Another action</a></li>
                  <li><a href="#">Something else here</a></li>
                  <li class="divider"></li>
                  <li><a href="#">Separated link</a></li>
                </ul>
              </div>
              <div class="btn-group">
                <button class="btn btn-default btn-small dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">Action</a></li>
                  <li><a href="#">Another action</a></li>
                  <li><a href="#">Something else here</a></li>
                  <li class="divider"></li>
                  <li><a href="#">Separated link</a></li>
                </ul>
              </div>
            </div>
            <div class="block">
              <div class="btn-group">
                <button class="btn btn-white btn-mini dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">Action</a></li>
                  <li><a href="#">Another action</a></li>
                  <li><a href="#">Something else here</a></li>
                  <li class="divider"></li>
                  <li><a href="#">Separated link</a></li>
                </ul>
              </div>
              <div class="btn-group">
                <button class="btn btn-default btn-mini dropdown-toggle" data-toggle="dropdown">Action <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">Action</a></li>
                  <li><a href="#">Another action</a></li>
                  <li><a href="#">Something else here</a></li>
                  <li class="divider"></li>
                  <li><a href="#">Separated link</a></li>
                </ul>
              </div>
            </div>
            <p>Split button dropdowns & variation </p>
            <div class="block">
              <div class="btn-group">
                <button class="btn btn-white">Action</button>
                <button class="btn btn-white dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">Action</a></li>
                  <li><a href="#">Another action</a></li>
                  <li><a href="#">Something else here</a></li>
                  <li class="divider"></li>
                  <li><a href="#">Separated link</a></li>
                </ul>
              </div>
              <div class="btn-group dropup">
                <button class="btn btn-default">Action</button>
                <button class="btn btn-default dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">Action</a></li>
                  <li><a href="#">Another action</a></li>
                  <li><a href="#">Something else here</a></li>
                  <li class="divider"></li>
                  <li><a href="#">Separated link</a></li>
                </ul>
              </div>
            </div>
          </section>
          <section class="panel">
            <header class="panel-heading">Button with icon</header>
            <p>
              <a href="#" class="btn btn-white"><i class="icon-html5"></i> Html5</a>
              <a href="#" class="btn btn-info"><i class="icon-css3"></i> CSS3</a>
            </p>            
            <p>
              <a href="#" class="btn btn-white btn-large btn-block"><i class="icon-reorder pull-right"></i> Block button with icon</a>
            </p>
            <p>
              <a href="#" class="btn btn-white btn-block"><i class="icon-reorder pull-left"></i> Block button with icon</a>
            </p>
          </section> 
        </div>
        <div class="col-lg-6">
          <section class="panel">
            <header class="panel-heading bg-white">
              Button groups
            </header>
            <div class="block">
              <div class="btn-group">
                <button type="button" class="btn btn-white">Left</button>
                <button type="button" class="btn btn-white">Middle</button>
                <button type="button" class="btn btn-white">Right</button>
              </div>
            </div>
            <p>Justified button groups</p>
            <div class="block">
              <div class="btn-group btn-group-justified">
                <a href="#" class="btn btn-primary">Left</a>
                <a href="#" class="btn btn-info">Middle</a>
                <a href="#" class="btn btn-success">Right</a>
              </div>
            </div>
            <p>Multiple button groups</p>
            <div class="btn-toolbar" style="margin: 0;">
              <div class="btn-group">
                <button type="button" class="btn">1</button>
                <button type="button" class="btn">2</button>
                <button type="button" class="btn">3</button>
                <button type="button" class="btn">4</button>
              </div>
              <div class="btn-group">
                <button type="button" class="btn">5</button>
                <button type="button" class="btn">6</button>
                <button type="button" class="btn">7</button>
              </div>
              <div class="btn-group">
                <button type="button" class="btn">8</button>
              </div>
            </div>
          </section>
          <section class="panel">
            <header class="panel-heading bg-white">
              <ul class="nav nav-pills pull-right">
                <li><a href="#" data-toggle="class:btn-circle" data-target="#circle-buttons a">Toggle</a></li>
              </ul>
              Circle button
            </header>
            <p>
              <small class="text-muted">Click the <a href="#" data-toggle="class:btn-circle" data-target="#circle-buttons a">Toggle</a> link to change them to normal button</small>
            </p>
            <p id="circle-buttons" class="doc-buttons">
              <a href="#" class="btn btn-circle"><i class="icon-envelope-alt"></i>Inbox <b class="badge bg-white">5</b></a>
              <a href="#" class="btn btn-primary btn-circle"><i class="icon-lightbulb"></i>Projects</a>
              <a href="#" class="btn btn-success btn-circle"><i class="icon-check"></i>Tasks</a>
              <a href="#" class="btn btn-info btn-circle"><i class="icon-bar-chart"></i>Stats</a>
              <a href="#" class="btn btn-inverse btn-circle"><i class="icon-time"></i>Timeline<b class="badge"><i class="icon-plus"></i></b></a>
              <a href="#" class="btn btn-warning btn-circle"><i class="icon-calendar-empty"></i>Calendar</a>
              <a href="#" class="btn btn-danger btn-circle"><i class="icon-group"></i>Groups</a>
              <a href="#" class="btn btn-white btn-circle"><i class="icon-plus"></i>More</a>
            </p>
            <small class="text-muted">You can change them to <a href="#" data-toggle="class:btn-small" data-target="#circle-buttons a">Small</a> buttons, also support the Mini and Large.</small>
            
          </section>
          <section class="panel">
            <header class="panel-heading">Button components</header>
            <p class="text-muted">
              <small>There are a few easy ways to quickly get started with Bootstrap, each one ...</small>
              <small class="text-muted hide" id="moreless"> appealing to a different skill level and use case. Read through to see what suits your particular needs.</small>
            </p>
            <p>
              <button href="#moreless" class="btn btn-small btn-white" data-toggle="class:show">
                <i class="icon-plus text"></i>
                <span class="text">More</span>
                <i class="icon-minus text-active"></i>
                <span class="text-active">Less</span>
              </button>
            </p>
            <p>
              <button class="btn btn-white" id="btn-1" href="#btn-1" data-toggle="class:btn-success">
                <i class="icon-cloud-upload text"></i>
                <span class="text">Upload</span>
                <i class="icon-ok text-active"></i>
                <span class="text-active">Success</span>
              </button>
              <button class="btn btn-white" data-toggle="button">
                <i class="icon-heart-empty text"></i>
                <i class="icon-heart text-active text-danger"></i>
              </button>
              <button class="btn btn-white" data-toggle="button">
                <span class="text">
                  <i class="icon-thumbs-up text-success"></i> 25
                </span>
                <span class="text-active">
                  <i class="icon-thumbs-down text-danger"></i> 10
                </span>
              </button>
            </p>
            <div class="block">
              <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-small btn-info active">
                  <input type="radio" name="options" id="option1"> <i class="icon-ok icon-large text-active"></i>Male
                </label>
                <label class="btn btn-small btn-danger">
                  <input type="radio" name="options" id="option2"> <i class="icon-ok icon-large text-active"></i>Female
                </label>
                <label class="btn btn-small btn-inverse">
                  <input type="radio" name="options" id="option3"> <i class="icon-ok icon-large text-active"></i>N/A
                </label>
              </div>
            </div>
            <div class="block">
              <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-small btn-white active">
                  <input type="radio" name="options" id="option1"> ALL
                </label>
                <label class="btn btn-small btn-white">
                  <input type="radio" name="options" id="option2"> ANY
                </label>
              </div>
              of the following
            </div>
          </section>
          <section class="panel doc-buttons">
            <header class="panel-heading">
              <ul class="nav nav-pills pull-right">
                <li><a href="#" data-toggle="class:btn-circle" data-target="#social-buttons a">Toggle</a></li>
              </ul>
              Social buttons
            </header>
            <p id="social-buttons">
              <a href="#" class="btn btn-circle btn-small btn-twitter"><i class="icon-twitter"></i> Twitter</a>
              <a href="#" class="btn btn-circle btn-small btn-facebook"><i class="icon-facebook"></i> Facebook</a>
              <a href="#" class="btn btn-circle btn-small btn-gplus"><i class="icon-google-plus"></i> Google+</a>              
            </p>
          </section>
        </div>
      </div>
    </section>
  </section>
  <!-- footer -->
  <footer id="footer">
    <div class="text-center padder clearfix">
      <p>
        <small>&copy; first 2013, Mobile first web app framework base on Bootstrap  更多模板：<a href="http://www.mycodes.net/" target="_blank">源码之家</a></small><br><br>
        <a href="#" class="btn btn-mini btn-circle btn-twitter"><i class="icon-twitter"></i></a>
        <a href="#" class="btn btn-mini btn-circle btn-facebook"><i class="icon-facebook"></i></a>
        <a href="#" class="btn btn-mini btn-circle btn-gplus"><i class="icon-google-plus"></i></a>
      </p>
    </div>
  </footer>
  <a href="#" class="hide slide-nav-block" data-toggle="class:slide-nav slide-nav-right" data-target="body"></a>
  <!-- / footer -->
	<script src="js/jquery.min.js"></script>
  <!-- Bootstrap -->
  <script src="js/bootstrap.js"></script>
  <!-- app -->
  <script src="js/app.js"></script>
  <script src="js/app.plugin.js"></script>
  <script src="js/app.data.js"></script>
</body>
</html>