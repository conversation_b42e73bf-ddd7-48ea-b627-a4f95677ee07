<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Mobile first web app theme | first</title>
  <meta name="description" content="mobile first, app, web app, responsive, admin dashboard, flat, flat ui">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"> 
  <link rel="stylesheet" href="css/bootstrap.css">
  <link rel="stylesheet" href="css/font-awesome.min.css">
  <link rel="stylesheet" href="css/style.css">
  <!--[if lt IE 9]>
    <script src="js/ie/respond.min.js"></script>
    <script src="js/ie/html5.js"></script>
  <![endif]-->
</head>
<body>
  <!-- header -->
  <header id="header" class="navbar">    
    <ul class="nav navbar-nav navbar-avatar pull-right">
      <li class="dropdown">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown">            
          <span class="hidden-sm-only">Mika <PERSON></span>
          <span class="thumb-small avatar inline"><img src="images/avatar.jpg" alt="Mika Sokeil" class="img-circle"></span>
          <b class="caret hidden-sm-only"></b>
        </a>
        <ul class="dropdown-menu">
          <li><a href="#">Settings</a></li>
          <li><a href="#">Profile</a></li>
          <li><a href="#"><span class="badge bg-danger pull-right">3</span>Notifications</a></li>
          <li class="divider"></li>
          <li><a href="docs.html">Help</a></li>
          <li><a href="signin.html">Logout</a></li>
        </ul>
      </li>
    </ul>
    <a class="navbar-brand" href="#">first</a>
    <button type="button" class="btn btn-link pull-left nav-toggle hidden-lg" data-toggle="class:show" data-target="#nav">
      <i class="icon-reorder icon-xlarge text-default"></i>
    </button>
    <ul class="nav navbar-nav hidden-sm">
      <li>
        <div class="m-t m-b-small" id="panel-notifications">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="icon-comment-alt icon-xlarge text-default"></i><b class="badge badge-notes bg-danger count-n">2</b></a>
          <section class="dropdown-menu m-l-small m-t-mini">
            <section class="panel panel-large arrow arrow-top">
              <header class="panel-heading bg-white"><span class="h5"><strong>You have <span class="count-n">2</span> notifications</strong></span></header>
              <div class="list-group list-group-flush m-t-n">
                <a href="#" class="media list-group-item">
                  <span class="pull-left thumb-small"><img src="images/avatar.jpg" alt="John said" class="img-circle"></span>
                  <span class="media-body block m-b-none">
                    Moved to Bootstrap 3.0<br>
                    <small class="text-muted">23 June 13</small>
                  </span>
                </a>
                <a href="#" class="media list-group-item">
                  <span class="media-body block m-b-none">
                    first v.1 (Bootstrap 2.3 based) released<br>
                    <small class="text-muted">19 June 13</small>
                  </span>
                </a>
              </div>
              <footer class="panel-footer text-small">
                <a href="#" class="pull-right"><i class="icon-cog"></i></a>
                <a href="#">See all the notifications</a>
              </footer>
            </section>
          </section>
        </div>
      </li>
      <li><div class="m-t-small"><a class="btn btn-small btn-info" data-toggle="modal" href="#modal"><i class="icon-plus"></i> POST</a></div></li>
      <li class="dropdown shift" data-toggle="shift:appendTo" data-target=".nav-primary .nav">
        <a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="icon-cog icon-xlarge visible-sm visible-sm-inline"></i>Settings <b class="caret hidden-sm-only"></b></a>
        <ul class="dropdown-menu">
          <li>
            <a href="#" data-toggle="class:navbar-fixed" data-target='body'>Navbar 
              <span class="text-active">auto</span>
              <span class="text">fixed</span>
            </a>
          </li>
          <li class="visible-lg">
            <a href="#" data-toggle="class:nav-vertical" data-target="#nav">Nav 
              <span class="text-active">vertical</span>
              <span class="text">horizontal</span>
            </a>
          </li>
          <li class="divider hidden-sm"></li>
          <li class="dropdown-header">Colors</li>
          <li>
            <a href="#" data-toggle="class:bg bg-black" data-target='.navbar'>Navbar 
              <span class="text-active">white</span>
              <span class="text">inverse</span>
            </a>
          </li>
          <li>
            <a href="#" data-toggle="class:bg-light" data-target='#nav'>Nav 
              <span class="text-active">inverse</span>
              <span class="text">light</span>
            </a>
          </li>
        </ul>
      </li>
    </ul>
    <form class="navbar-form pull-left shift" action="" data-toggle="shift:appendTo" data-target=".nav-primary">
      <i class="icon-search text-muted"></i>
      <input type="text" class="input-small form-control" placeholder="Search">
    </form>
  </header>
  <!-- / header -->
  <!-- nav -->
  <nav id="nav" class="nav-primary visible-lg nav-vertical">
    <ul class="nav" data-spy="affix" data-offset-top="50">
      <li><a href="index.html"><i class="icon-dashboard icon-xlarge"></i>Dashboard</a></li>
      <li class="dropdown-submenu">
        <a href="#"><i class="icon-th icon-xlarge"></i>Elements</a>
        <ul class="dropdown-menu">
          <li><a href="buttons.html">Buttons</a></li>
          <li><a href="icons.html"><b class="badge pull-right">302</b>Icons</a></li>            
          <li><a href="grid.html">Grid</a></li>
          <li><a href="widgets.html"><b class="badge bg-primary pull-right">8</b>Widgets</a></li>
          <li><a href="components.html"><b class="badge pull-right">18</b>Components</a></li>
        </ul>
      </li>
      <li class="dropdown-submenu">
        <a href="#"><i class="icon-list icon-xlarge"></i>Lists</a>
        <ul class="dropdown-menu">
          <li><a href="list.html">List groups</a></li>
          <li><a href="table.html">Table</a></li>
        </ul>
      </li>
      <li><a href="form.html"><i class="icon-edit icon-xlarge"></i>Form</a></li>
      <li class="active"><a href="chart.html"><i class="icon-signal icon-xlarge"></i>Chart</a></li>
      <li class="dropdown-submenu">
        <a href="#"><i class="icon-link icon-xlarge"></i>Others</a>
        <ul class="dropdown-menu">
          <li><a href="signin.html">Signin page</a></li>
          <li><a href="signup.html">Signup page</a></li>
          <li><a href="404.html">404 page</a></li>
        </ul>
      </li>
    </ul>
  </nav>
  <!-- / nav -->
  <section id="content">
    <section class="main padder">
      <div class="clearfix">
        <h4>Chart</h4>
      </div>
      <div class="row">
        <div class="col-lg-8">
          <section class="panel">
            <header class="panel-heading">
              Stats
            </header>
            <div class="btn-group" data-toggle="buttons">
              <label class="btn btn-small btn-white active">
                <input type="radio" name="options" id="option1"> Day
              </label>
              <label class="btn btn-small btn-white">
                <input type="radio" name="options" id="option2"> Week
              </label>
              <label class="btn btn-small btn-white">
                <input type="radio" name="options" id="option2"> Month
              </label>
              <label class="btn btn-small btn-white">
                <input type="radio" name="options" id="option2"> Year
              </label>
            </div>
            <div class="line line-large pull-in"></div>
            <div class="sparkline" data-type="line" data-resize="true" data-height="200" data-width="100%" data-line-color="#bfea5f" data-fill-color="#f3fce3" data-highlight-line-color="#e1e5e9" data-spot-radius="5" data-composite-data="[160,230,250,300,320,330,280,260,250,280,250,260,250,255,330,345,300,210,200,200,170,180,250,250,200,200,280,270,310,250,280,175]" data-composite-line-color="#a3e2fe" data-composite-fill-color="#e3f6ff" data-data="[120,250,200,325,400,380,250,320,345,250,250,250,200,325,300,365,250,210,200,180,150,160,250,250,250,200,300,310,330,250,320,205]"></div>
            <ul class="list-inline text-muted axis"><li>12:00<br>am</li><li>2:00</li><li>4:00</li><li>6:00</li><li>8:00</li><li>10:00</li><li>12:00<br>pm</li><li>2:00</li><li>4:00</li><li>6:00</li><li>8:00</li><li>10:00</li></ul>
          </section>
        </div>
        <div class="col-lg-4">
          <section class="panel">
            <header class="panel-heading">
              Conversion
            </header>
            <div class="pull-in text-center">
              <h4>62.5<small> hrs</small></h4>
              <small class="text-muted block">Updated at 2 minutes ago</small>
              <div class="inline">
                <div class="easypiechart" data-percent="75" data-line-width="16" data-loop="false" data-size="188">
                  <span class="h2" style="margin-left:10px;margin-top:10px;display:inline-block">75</span>%
                  <div class="easypie-text"><button class="btn btn-link m-t-n-small" data-toggle="class:pie"><i class="icon-play text text-muted"></i><i class="icon-pause text-active text-muted"></i></button></div>
                </div>
              </div>
              <div class="line"></div>
              <div><small>% of avarage rate of the visits</small></div>
            </div>
          </section>
        </div>
      </div>
      <div class="row">
        <div class="col-lg-4">
          <section class="panel">
            <header class="panel-heading">Pie</header>
            <div class="text-center">              
              <div class="sparkline inline" data-type="pie" data-height="175" data-slice-colors="['#233445','#3fcf7f','#ff5f5f','#f4c414','#13c4a5']">20,15,5,40,20</div>
              <div class="line pull-in"></div>
              <div class="text-mini">
                <i class="icon-circle text-info"></i> 25%
                <i class="icon-circle text-success"></i> 15%
                <i class="icon-circle text-danger"></i> 25%
                <i class="icon-circle text-primary"></i> 18%
                <i class="icon-circle text-warning"></i> 40%
              </div>
            </div>
          </section>
        </div>
        <div class="col-lg-4">
          <section class="panel">
            <header class="panel-heading">Composite</header>
            <div class="text-center">
              <div class="inline">
                <div class="sparkline inline" data-type="bar" data-height="145" data-bar-width="20" data-bar-spacing="10" data-bar-color="#a3e2fe" data-composite-data="[10,8,12,13,22,15,18]" data-composite-line-color="#bfea5f" data-composite-fill-color="#f3fce3" data-highlight-line-color="#e1e5e9" data-spot-radius="3">5,15,12,18,20,17,13</div>
                <ul class="list-inline text-muted axis"><li>M<br>5%</li><li>T<br>15%</li><li>W<br>12%</li><li>T<br>18%</li><li>F<br>20%</li><li>S<br>17%</li><li>S<br>13%</li></ul>
              </div>
              <div class="line pull-in"></div>
              <div class="text-small">Check more data</div>
            </div>
          </section>
        </div>
        <div class="col-lg-4">
          <section class="panel">
            <header class="panel-heading">Breakdown</header>
            <div class="media">
              <div class="pull-right media-small">Today</div>
              <div class="progress bg-light">
                <div class="progress-bar bg-primary" style="width: 65%">
                  $20,000</div>
              </div>
            </div>
            <div class="media m-t-none">
              <div class="pull-right media-small">Yesterday</div>
              <div class="progress bg-light">
                <div class="progress-bar bg-success" style="width: 80%">
                  $30,000</div>
              </div>
            </div>
            <div class="media m-t-none">
              <div class="pull-right media-small">Friday</div>
              <div class="progress bg-light">
                <div class="progress-bar bg-info" style="width: 60%">
                  $15,000</div>
              </div>
            </div>
            <div class="media m-t-none">
              <div class="pull-right media-small">Thursday</div>
              <div class="progress bg-light">
                <div class="progress-bar bg-warning" style="width: 55%">
                  $11,000</div>
              </div>
            </div>
            <div class="media m-t-none">
              <div class="pull-right media-small">Wednesday</div>
              <div class="progress bg-light">
                <div class="progress-bar bg-danger" style="width: 50%">
                  $9,000</div>
              </div>
            </div>
            <div class="media m-t-none">
              <div class="pull-right media-small">Tuesday</div>
              <div class="progress bg-light">
                <div class="progress-bar bg-default" style="width: 35%">
                  $7,500</div>
              </div>
            </div>
            <div class="media m-t-none">
              <div class="pull-right media-small">Monday</div>
              <div class="progress bg-light">
                <div class="progress-bar bg-default" style="width: 40%">
                  $8,000</div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </section>
  </section>
  <!-- footer -->
  <footer id="footer">
    <div class="text-center padder clearfix">
      <p>
        <small>&copy; first 2013, Mobile first web app framework base on Bootstrap  更多模板：<a href="http://www.mycodes.net/" target="_blank">源码之家</a></small><br><br>
        <a href="#" class="btn btn-mini btn-circle btn-twitter"><i class="icon-twitter"></i></a>
        <a href="#" class="btn btn-mini btn-circle btn-facebook"><i class="icon-facebook"></i></a>
        <a href="#" class="btn btn-mini btn-circle btn-gplus"><i class="icon-google-plus"></i></a>
      </p>
    </div>
  </footer>
  <!-- / footer -->
	<script src="js/jquery.min.js"></script>
  <!-- Bootstrap -->
  <script src="js/bootstrap.js"></script>
  <!-- app -->
  <script src="js/app.js"></script>
  <script src="js/app.plugin.js"></script>
  <script src="js/app.data.js"></script>
  <!-- Sparkline Chart -->
  <script src="js/charts/sparkline/jquery.sparkline.min.js"></script>
  <script src="js/ie/excanvas.js"></script>
  <!-- Easy Pie Chart -->
  <script src="js/charts/easypiechart/jquery.easy-pie-chart.js"></script>
</body>
</html>