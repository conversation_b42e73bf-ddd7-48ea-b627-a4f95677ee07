# 智慧医疗系统测试指南

## 系统启动流程

1. **启动应用**
   ```bash
   cd /Users/<USER>/PyCode/codeWebV1
   python app.py
   ```

2. **访问系统**
   - 打开浏览器访问：`http://localhost:8887`
   - 应该看到登录页面

## 测试路径

### 路径1：快速体验（推荐）
1. 点击"快速体验（无需登录）"绿色按钮
2. 自动进入管理员首页
3. 可以点击"前台用户中心"体验用户功能

### 路径2：正常登录
1. 输入任意用户名和密码（如：admin/123456）
2. 点击"登录系统"
3. 进入原版后台管理页面

### 路径3：注册新用户
1. 点击"立即注册"
2. 填写任意信息
3. 注册后进入用户前台

## 功能模块测试

### 后台管理功能
- `/admin` - 原版后台管理
- `/admin/index` - 新版管理员首页
- `/table` - 患者列表
- `/chart` - 数据可视化
- `/relations` - 疾病关联分析

### 前台用户功能
- `/user` - 用户中心首页
- `/user/personal_info` - 个人数据录入
- `/user/query_report` - 查看报告
- `/user/health_dashboard` - 健康仪表盘
- `/user/health_tips` - 健康提醒
- `/user/health_archive` - 健康档案管理
- `/user/login_system` - 登录系统

## 预期行为

1. **首次访问** → 显示登录页面
2. **快速体验** → 直接进入管理员首页，可以体验所有功能
3. **正常登录** → 根据用户类型跳转到对应页面
4. **页面跳转** → 所有链接都应该正常工作
5. **权限控制** → 未登录用户访问受保护页面会跳转到登录页

## 可能的问题

1. **Flask未安装** → 运行 `pip install flask`
2. **端口被占用** → 修改app.py中的端口号
3. **模板文件缺失** → 检查templates目录结构
4. **静态文件404** → 检查static目录是否存在

## 系统特点

- ✅ 双端设计：管理后台 + 用户前台
- ✅ 假登录系统：任意输入都可以成功
- ✅ 快速体验：无需登录直接使用
- ✅ 完整功能：包含所有原有功能
- ✅ 响应式设计：支持移动端访问
- ✅ 美观界面：现代化UI设计
