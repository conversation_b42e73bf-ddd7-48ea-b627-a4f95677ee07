<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>

</head>
<body>
    <div id="945d33ae1d22460a9928c4ac7c239eca" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_945d33ae1d22460a9928c4ac7c239eca = echarts.init(
            document.getElementById('945d33ae1d22460a9928c4ac7c239eca'), 'white', {renderer: 'canvas'});
        var option_945d33ae1d22460a9928c4ac7c239eca = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "graph",
            "layout": "force",
            "symbolSize": 10,
            "circular": {
                "rotateLabel": false
            },
            "force": {
                "repulsion": 8000,
                "edgeLength": 50,
                "gravity": 0.2
            },
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "lineStyle": {
                "show": true,
                "width": 1,
                "opacity": 1,
                "curveness": 0,
                "type": "solid"
            },
            "roam": true,
            "draggable": false,
            "focusNodeAdjacency": true,
            "data": [
                {
                    "name": "\u542b\u6709\u75bc\u75db\u75c5\u51b5",
                    "symbolSize": 10
                },
                {
                    "name": "\u5c31\u8fd1\u65b9\u4fbf\u674e\u5927\u7237",
                    "symbolSize": 20
                },
                {
                    "name": "\u5f20\u4e09",
                    "symbolSize": 20
                },
                {
                    "name": "\u5c0f\u674e",
                    "symbolSize": 20
                }
            ],
            "edgeLabel": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "edgeSymbol": [
                null,
                null
            ],
            "edgeSymbolSize": 10,
            "links": [
                {
                    "source": "\u542b\u6709\u75bc\u75db\u75c5\u51b5",
                    "target": "\u542b\u6709\u75bc\u75db\u75c5\u51b5"
                },
                {
                    "source": "\u542b\u6709\u75bc\u75db\u75c5\u51b5",
                    "target": "\u5c31\u8fd1\u65b9\u4fbf\u674e\u5927\u7237"
                },
                {
                    "source": "\u542b\u6709\u75bc\u75db\u75c5\u51b5",
                    "target": "\u5f20\u4e09"
                },
                {
                    "source": "\u542b\u6709\u75bc\u75db\u75c5\u51b5",
                    "target": "\u5c0f\u674e"
                },
                {
                    "source": "\u5c31\u8fd1\u65b9\u4fbf\u674e\u5927\u7237",
                    "target": "\u542b\u6709\u75bc\u75db\u75c5\u51b5"
                },
                {
                    "source": "\u5c31\u8fd1\u65b9\u4fbf\u674e\u5927\u7237",
                    "target": "\u5c31\u8fd1\u65b9\u4fbf\u674e\u5927\u7237"
                },
                {
                    "source": "\u5c31\u8fd1\u65b9\u4fbf\u674e\u5927\u7237",
                    "target": "\u5f20\u4e09"
                },
                {
                    "source": "\u5c31\u8fd1\u65b9\u4fbf\u674e\u5927\u7237",
                    "target": "\u5c0f\u674e"
                },
                {
                    "source": "\u5f20\u4e09",
                    "target": "\u542b\u6709\u75bc\u75db\u75c5\u51b5"
                },
                {
                    "source": "\u5f20\u4e09",
                    "target": "\u5c31\u8fd1\u65b9\u4fbf\u674e\u5927\u7237"
                },
                {
                    "source": "\u5f20\u4e09",
                    "target": "\u5f20\u4e09"
                },
                {
                    "source": "\u5f20\u4e09",
                    "target": "\u5c0f\u674e"
                },
                {
                    "source": "\u5c0f\u674e",
                    "target": "\u542b\u6709\u75bc\u75db\u75c5\u51b5"
                },
                {
                    "source": "\u5c0f\u674e",
                    "target": "\u5c31\u8fd1\u65b9\u4fbf\u674e\u5927\u7237"
                },
                {
                    "source": "\u5c0f\u674e",
                    "target": "\u5f20\u4e09"
                },
                {
                    "source": "\u5c0f\u674e",
                    "target": "\u5c0f\u674e"
                }
            ]
        }
    ],
    "legend": [
        {
            "data": [],
            "selected": {},
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "title": [
        {
            "text": "\u75be\u75c5\u5173\u8054\u5206\u6790",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_945d33ae1d22460a9928c4ac7c239eca.setOption(option_945d33ae1d22460a9928c4ac7c239eca);
    </script>
</body>
</html>
