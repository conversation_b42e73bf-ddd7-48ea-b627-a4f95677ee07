<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="172">
            <item index="0" class="java.lang.String" itemvalue="flask-moment" />
            <item index="1" class="java.lang.String" itemvalue="Flask-Script" />
            <item index="2" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="3" class="java.lang.String" itemvalue="wheel" />
            <item index="4" class="java.lang.String" itemvalue="Flask-WTF" />
            <item index="5" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="6" class="java.lang.String" itemvalue="psycopg2" />
            <item index="7" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="8" class="java.lang.String" itemvalue="gunicorn" />
            <item index="9" class="java.lang.String" itemvalue="Flask-SQLAlchemy" />
            <item index="10" class="java.lang.String" itemvalue="Jinja2" />
            <item index="11" class="java.lang.String" itemvalue="Flask-Bootstrap" />
            <item index="12" class="java.lang.String" itemvalue="ForgeryPy" />
            <item index="13" class="java.lang.String" itemvalue="WTForms" />
            <item index="14" class="java.lang.String" itemvalue="flask-login" />
            <item index="15" class="java.lang.String" itemvalue="Flask-Migrate" />
            <item index="16" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="17" class="java.lang.String" itemvalue="Flask" />
            <item index="18" class="java.lang.String" itemvalue="altgraph" />
            <item index="19" class="java.lang.String" itemvalue="pyinstaller" />
            <item index="20" class="java.lang.String" itemvalue="opencv-python" />
            <item index="21" class="java.lang.String" itemvalue="numpy" />
            <item index="22" class="java.lang.String" itemvalue="requests" />
            <item index="23" class="java.lang.String" itemvalue="loguru" />
            <item index="24" class="java.lang.String" itemvalue="click" />
            <item index="25" class="java.lang.String" itemvalue="pefile" />
            <item index="26" class="java.lang.String" itemvalue="pyinstaller-hooks-contrib" />
            <item index="27" class="java.lang.String" itemvalue="chardet" />
            <item index="28" class="java.lang.String" itemvalue="win32-setctime" />
            <item index="29" class="java.lang.String" itemvalue="certifi" />
            <item index="30" class="java.lang.String" itemvalue="pywin32-ctypes" />
            <item index="31" class="java.lang.String" itemvalue="PyExecJS" />
            <item index="32" class="java.lang.String" itemvalue="urllib3" />
            <item index="33" class="java.lang.String" itemvalue="idna" />
            <item index="34" class="java.lang.String" itemvalue="Pillow" />
            <item index="35" class="java.lang.String" itemvalue="absl-py" />
            <item index="36" class="java.lang.String" itemvalue="google-pasta" />
            <item index="37" class="java.lang.String" itemvalue="bs4" />
            <item index="38" class="java.lang.String" itemvalue="greenlet" />
            <item index="39" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="40" class="java.lang.String" itemvalue="PyYAML" />
            <item index="41" class="java.lang.String" itemvalue="MyQQ-http" />
            <item index="42" class="java.lang.String" itemvalue="PyQt5-sip" />
            <item index="43" class="java.lang.String" itemvalue="pycparser" />
            <item index="44" class="java.lang.String" itemvalue="gitdb" />
            <item index="45" class="java.lang.String" itemvalue="redis" />
            <item index="46" class="java.lang.String" itemvalue="pyasn1-modules" />
            <item index="47" class="java.lang.String" itemvalue="patsy" />
            <item index="48" class="java.lang.String" itemvalue="tables" />
            <item index="49" class="java.lang.String" itemvalue="PyQt5" />
            <item index="50" class="java.lang.String" itemvalue="astunparse" />
            <item index="51" class="java.lang.String" itemvalue="lxml" />
            <item index="52" class="java.lang.String" itemvalue="soupsieve" />
            <item index="53" class="java.lang.String" itemvalue="gevent" />
            <item index="54" class="java.lang.String" itemvalue="comtypes" />
            <item index="55" class="java.lang.String" itemvalue="PyQRCode" />
            <item index="56" class="java.lang.String" itemvalue="Panda3D" />
            <item index="57" class="java.lang.String" itemvalue="xlrd" />
            <item index="58" class="java.lang.String" itemvalue="pywin32" />
            <item index="59" class="java.lang.String" itemvalue="libclang" />
            <item index="60" class="java.lang.String" itemvalue="pydantic" />
            <item index="61" class="java.lang.String" itemvalue="pyperclip" />
            <item index="62" class="java.lang.String" itemvalue="uncompyle" />
            <item index="63" class="java.lang.String" itemvalue="tensorboard-data-server" />
            <item index="64" class="java.lang.String" itemvalue="QDarkStyle" />
            <item index="65" class="java.lang.String" itemvalue="xdis" />
            <item index="66" class="java.lang.String" itemvalue="attrs" />
            <item index="67" class="java.lang.String" itemvalue="mpl-finance" />
            <item index="68" class="java.lang.String" itemvalue="PyQtWebEngine" />
            <item index="69" class="java.lang.String" itemvalue="simplejson" />
            <item index="70" class="java.lang.String" itemvalue="flatbuffers" />
            <item index="71" class="java.lang.String" itemvalue="regex" />
            <item index="72" class="java.lang.String" itemvalue="tensorboard" />
            <item index="73" class="java.lang.String" itemvalue="matplotlib" />
            <item index="74" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="75" class="java.lang.String" itemvalue="pytdx" />
            <item index="76" class="java.lang.String" itemvalue="rsa" />
            <item index="77" class="java.lang.String" itemvalue="netifaces" />
            <item index="78" class="java.lang.String" itemvalue="smmap" />
            <item index="79" class="java.lang.String" itemvalue="cffi" />
            <item index="80" class="java.lang.String" itemvalue="wget" />
            <item index="81" class="java.lang.String" itemvalue="pyasn1" />
            <item index="82" class="java.lang.String" itemvalue="sniffio" />
            <item index="83" class="java.lang.String" itemvalue="threadpool" />
            <item index="84" class="java.lang.String" itemvalue="websocket-client" />
            <item index="85" class="java.lang.String" itemvalue="tensorflow" />
            <item index="86" class="java.lang.String" itemvalue="tensorboard-plugin-wit" />
            <item index="87" class="java.lang.String" itemvalue="Deprecated" />
            <item index="88" class="java.lang.String" itemvalue="PyQtWebEngine-Qt5" />
            <item index="89" class="java.lang.String" itemvalue="scipy" />
            <item index="90" class="java.lang.String" itemvalue="cfscrape" />
            <item index="91" class="java.lang.String" itemvalue="tornado" />
            <item index="92" class="java.lang.String" itemvalue="google-auth-oauthlib" />
            <item index="93" class="java.lang.String" itemvalue="spark-parser" />
            <item index="94" class="java.lang.String" itemvalue="graia-application-mirai" />
            <item index="95" class="java.lang.String" itemvalue="et-xmlfile" />
            <item index="96" class="java.lang.String" itemvalue="tensorflow-io-gcs-filesystem" />
            <item index="97" class="java.lang.String" itemvalue="pandas" />
            <item index="98" class="java.lang.String" itemvalue="termcolor" />
            <item index="99" class="java.lang.String" itemvalue="future" />
            <item index="100" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="101" class="java.lang.String" itemvalue="cachetools" />
            <item index="102" class="java.lang.String" itemvalue="statsmodels" />
            <item index="103" class="java.lang.String" itemvalue="multidict" />
            <item index="104" class="java.lang.String" itemvalue="cloudscraper" />
            <item index="105" class="java.lang.String" itemvalue="yarl" />
            <item index="106" class="java.lang.String" itemvalue="pytz" />
            <item index="107" class="java.lang.String" itemvalue="requests-toolbelt" />
            <item index="108" class="java.lang.String" itemvalue="mysqlclient" />
            <item index="109" class="java.lang.String" itemvalue="protobuf" />
            <item index="110" class="java.lang.String" itemvalue="tushare" />
            <item index="111" class="java.lang.String" itemvalue="graia-broadcast" />
            <item index="112" class="java.lang.String" itemvalue="pygame" />
            <item index="113" class="java.lang.String" itemvalue="joblib" />
            <item index="114" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="115" class="java.lang.String" itemvalue="opt-einsum" />
            <item index="116" class="java.lang.String" itemvalue="nltk" />
            <item index="117" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="118" class="java.lang.String" itemvalue="QtPy" />
            <item index="119" class="java.lang.String" itemvalue="cycler" />
            <item index="120" class="java.lang.String" itemvalue="gast" />
            <item index="121" class="java.lang.String" itemvalue="frozenlist" />
            <item index="122" class="java.lang.String" itemvalue="Logbook" />
            <item index="123" class="java.lang.String" itemvalue="tflearn" />
            <item index="124" class="java.lang.String" itemvalue="PyQt5-Qt5" />
            <item index="125" class="java.lang.String" itemvalue="ui" />
            <item index="126" class="java.lang.String" itemvalue="oauthlib" />
            <item index="127" class="java.lang.String" itemvalue="keras" />
            <item index="128" class="java.lang.String" itemvalue="mysql" />
            <item index="129" class="java.lang.String" itemvalue="pyparsing" />
            <item index="130" class="java.lang.String" itemvalue="Markdown" />
            <item index="131" class="java.lang.String" itemvalue="wxpy" />
            <item index="132" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="133" class="java.lang.String" itemvalue="h5py" />
            <item index="134" class="java.lang.String" itemvalue="wrapt" />
            <item index="135" class="java.lang.String" itemvalue="cryptography" />
            <item index="136" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="137" class="java.lang.String" itemvalue="zope.interface" />
            <item index="138" class="java.lang.String" itemvalue="pywinauto" />
            <item index="139" class="java.lang.String" itemvalue="uncompyle6" />
            <item index="140" class="java.lang.String" itemvalue="fonttools" />
            <item index="141" class="java.lang.String" itemvalue="cssselect" />
            <item index="142" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="143" class="java.lang.String" itemvalue="itchat" />
            <item index="144" class="java.lang.String" itemvalue="numexpr" />
            <item index="145" class="java.lang.String" itemvalue="jieba" />
            <item index="146" class="java.lang.String" itemvalue="async-timeout" />
            <item index="147" class="java.lang.String" itemvalue="sklearn" />
            <item index="148" class="java.lang.String" itemvalue="mplfinance" />
            <item index="149" class="java.lang.String" itemvalue="requests-oauthlib" />
            <item index="150" class="java.lang.String" itemvalue="Keras-Preprocessing" />
            <item index="151" class="java.lang.String" itemvalue="xmltodict" />
            <item index="152" class="java.lang.String" itemvalue="zope.event" />
            <item index="153" class="java.lang.String" itemvalue="tf-estimator-nightly" />
            <item index="154" class="java.lang.String" itemvalue="pypng" />
            <item index="155" class="java.lang.String" itemvalue="pymongo" />
            <item index="156" class="java.lang.String" itemvalue="six" />
            <item index="157" class="java.lang.String" itemvalue="xlutils" />
            <item index="158" class="java.lang.String" itemvalue="pytesseract" />
            <item index="159" class="java.lang.String" itemvalue="xlwt" />
            <item index="160" class="java.lang.String" itemvalue="packaging" />
            <item index="161" class="java.lang.String" itemvalue="tqdm" />
            <item index="162" class="java.lang.String" itemvalue="colorama" />
            <item index="163" class="java.lang.String" itemvalue="aiohttp" />
            <item index="164" class="java.lang.String" itemvalue="grpcio" />
            <item index="165" class="java.lang.String" itemvalue="async-lru" />
            <item index="166" class="java.lang.String" itemvalue="pyquery" />
            <item index="167" class="java.lang.String" itemvalue="aiosignal" />
            <item index="168" class="java.lang.String" itemvalue="google-auth" />
            <item index="169" class="java.lang.String" itemvalue="openpyxl" />
            <item index="170" class="java.lang.String" itemvalue="django-compressor" />
            <item index="171" class="java.lang.String" itemvalue="django-filters" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="app.dict_activities" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>