# 患者管理系统 ER图关系说明

## 数据库表结构概览

### 核心实体表 (8个表)

1. **users** - 患者基本信息表
2. **departments** - 医院科室表  
3. **doctors** - 医生信息表
4. **medicines** - 药品信息表
5. **medical_records** - 病历记录表
6. **prescriptions** - 处方记录表
7. **appointments** - 预约挂号表
8. **examinations** - 检查项目表
9. **billing** - 费用记录表

## 表关系详细说明

### 1. 一对多关系 (One-to-Many)

#### departments → doctors
- **关系**: 一个科室可以有多个医生
- **外键**: `doctors.department_id` → `departments.id`
- **约束**: ON DELETE SET NULL (科室删除时，医生的科室ID设为NULL)

#### users → medical_records  
- **关系**: 一个患者可以有多条病历记录
- **外键**: `medical_records.patient_id` → `users.id`
- **约束**: ON DELETE CASCADE (患者删除时，相关病历也删除)

#### doctors → medical_records
- **关系**: 一个医生可以创建多条病历记录  
- **外键**: `medical_records.doctor_id` → `doctors.id`
- **约束**: ON DELETE CASCADE (医生删除时，相关病历也删除)

#### users → appointments
- **关系**: 一个患者可以有多个预约记录
- **外键**: `appointments.patient_id` → `users.id`
- **约束**: ON DELETE CASCADE (患者删除时，相关预约也删除)

#### doctors → appointments  
- **关系**: 一个医生可以有多个预约记录
- **外键**: `appointments.doctor_id` → `doctors.id`
- **约束**: ON DELETE CASCADE (医生删除时，相关预约也删除)

#### medical_records → prescriptions
- **关系**: 一条病历可以有多个处方记录
- **外键**: `prescriptions.medical_record_id` → `medical_records.id`
- **约束**: ON DELETE CASCADE (病历删除时，相关处方也删除)

#### medicines → prescriptions
- **关系**: 一种药品可以出现在多个处方中
- **外键**: `prescriptions.medicine_id` → `medicines.id`  
- **约束**: ON DELETE CASCADE (药品删除时，相关处方也删除)

#### medical_records → examinations
- **关系**: 一条病历可以有多个检查项目
- **外键**: `examinations.medical_record_id` → `medical_records.id`
- **约束**: ON DELETE CASCADE (病历删除时，相关检查也删除)

#### users → billing
- **关系**: 一个患者可以有多条费用记录
- **外键**: `billing.patient_id` → `users.id`
- **约束**: ON DELETE CASCADE (患者删除时，相关费用记录也删除)

#### medical_records → billing (可选关联)
- **关系**: 一条病历可以对应多条费用记录
- **外键**: `billing.medical_record_id` → `medical_records.id`
- **约束**: ON DELETE SET NULL (病历删除时，费用记录的病历ID设为NULL)

## 业务流程关系图

```
患者(users) 
    ↓
预约挂号(appointments) ← 医生(doctors) ← 科室(departments)
    ↓
病历记录(medical_records)
    ↓
    ├── 处方记录(prescriptions) → 药品(medicines)
    ├── 检查项目(examinations)  
    └── 费用记录(billing)
```

## 主要字段说明

### 枚举字段
- **doctors.gender**: '男', '女'
- **appointments.status**: '已预约', '已完成', '已取消', '爽约'
- **examinations.status**: '待检查', '已完成', '异常'  
- **billing.item_type**: '挂号费', '诊疗费', '药品费', '检查费', '治疗费', '其他'
- **billing.payment_status**: '未支付', '已支付', '部分支付', '已退款'

### 索引设计
- 所有外键字段都建立了索引
- 重要的查询字段建立了索引（如日期、状态等）
- 主键自动建立聚集索引

### 数据类型规范
- **ID字段**: INT(11) UNSIGNED AUTO_INCREMENT
- **姓名字段**: VARCHAR(100) 
- **描述字段**: TEXT
- **金额字段**: DECIMAL(10, 2)
- **日期字段**: DATE / DATETIME / TIMESTAMP
- **状态字段**: ENUM

## ER图生成建议

使用提供的 `patient_system_structure.sql` 文件可以生成完整的ER图，图中将显示：

1. **实体框**: 9个表作为实体
2. **关系线**: 表示外键关联关系
3. **基数标注**: 一对多关系的基数
4. **字段列表**: 每个表的完整字段信息
5. **主键标识**: 主键字段的特殊标记
6. **外键标识**: 外键字段和引用关系

建议使用支持MySQL的ER图工具，如：
- MySQL Workbench
- Navicat
- phpMyAdmin
- dbdiagram.io
- draw.io

这样可以清晰地展示整个患者管理系统的数据库设计和表关系结构。
