/*
 Enhanced Patient Management System Database Structure
 用于生成ER图的数据库结构脚本
 
 Date: 2023-04-05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ============================
-- 患者表 (原始表)
-- ============================
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '患者ID',
  `name` VARCHAR(100) NOT NULL COMMENT '患者姓名',
  `age` INT(3) NULL DEFAULT NULL COMMENT '年龄',
  `blood` VARCHAR(10) NULL DEFAULT NULL COMMENT '血型',
  `behavior` VARCHAR(50) NULL DEFAULT NULL COMMENT '就医行为类型',
  `detail` VARCHAR(500) NULL DEFAULT NULL COMMENT '病情详情',
  `date` DATE NULL DEFAULT NULL COMMENT '登记日期',
  `doc_resp` VARCHAR(500) NULL DEFAULT NULL COMMENT '医生建议',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '患者基本信息表';

-- ============================
-- 科室表
-- ============================
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '科室ID',
  `name` VARCHAR(100) NOT NULL COMMENT '科室名称',
  `description` TEXT NULL COMMENT '科室描述',
  `location` VARCHAR(100) NULL DEFAULT NULL COMMENT '科室位置',
  `phone` VARCHAR(20) NULL DEFAULT NULL COMMENT '科室电话',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '医院科室表';

-- ============================
-- 医生表
-- ============================
DROP TABLE IF EXISTS `doctors`;
CREATE TABLE `doctors` (
  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '医生ID',
  `name` VARCHAR(100) NOT NULL COMMENT '医生姓名',
  `gender` ENUM('男','女') NULL DEFAULT NULL COMMENT '性别',
  `age` INT(3) NULL DEFAULT NULL COMMENT '年龄',
  `department_id` INT(11) UNSIGNED NULL DEFAULT NULL COMMENT '所属科室ID',
  `title` VARCHAR(50) NULL DEFAULT NULL COMMENT '职称',
  `specialization` VARCHAR(200) NULL DEFAULT NULL COMMENT '专业特长',
  `phone` VARCHAR(20) NULL DEFAULT NULL COMMENT '联系电话',
  `email` VARCHAR(100) NULL DEFAULT NULL COMMENT '邮箱',
  `years_experience` INT(2) NULL DEFAULT NULL COMMENT '从业年限',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_department_id`(`department_id`) USING BTREE,
  CONSTRAINT `fk_doctor_department` FOREIGN KEY (`department_id`) 
    REFERENCES `departments` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '医生信息表';

-- ============================
-- 药品表
-- ============================
DROP TABLE IF EXISTS `medicines`;
CREATE TABLE `medicines` (
  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '药品ID',
  `name` VARCHAR(100) NOT NULL COMMENT '药品名称',
  `generic_name` VARCHAR(100) NULL DEFAULT NULL COMMENT '通用名',
  `category` VARCHAR(50) NULL DEFAULT NULL COMMENT '药品分类',
  `specification` VARCHAR(100) NULL DEFAULT NULL COMMENT '规格',
  `unit` VARCHAR(20) NULL DEFAULT NULL COMMENT '单位',
  `price` DECIMAL(10, 2) NULL DEFAULT NULL COMMENT '单价',
  `manufacturer` VARCHAR(100) NULL DEFAULT NULL COMMENT '生产厂家',
  `description` TEXT NULL COMMENT '药品说明',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '药品信息表';

-- ============================
-- 病历表
-- ============================
DROP TABLE IF EXISTS `medical_records`;
CREATE TABLE `medical_records` (
  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '病历ID',
  `patient_id` INT(11) UNSIGNED NOT NULL COMMENT '患者ID',
  `doctor_id` INT(11) UNSIGNED NOT NULL COMMENT '医生ID',
  `visit_date` DATETIME NOT NULL COMMENT '就诊日期',
  `chief_complaint` TEXT NULL COMMENT '主诉',
  `present_illness` TEXT NULL COMMENT '现病史',
  `past_history` TEXT NULL COMMENT '既往史',
  `physical_examination` TEXT NULL COMMENT '体格检查',
  `diagnosis` TEXT NULL COMMENT '诊断',
  `treatment_plan` TEXT NULL COMMENT '治疗方案',
  `notes` TEXT NULL COMMENT '备注',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_doctor_id`(`doctor_id`) USING BTREE,
  INDEX `idx_visit_date`(`visit_date`) USING BTREE,
  CONSTRAINT `fk_record_patient` FOREIGN KEY (`patient_id`) 
    REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_record_doctor` FOREIGN KEY (`doctor_id`) 
    REFERENCES `doctors` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '病历记录表';

-- ============================
-- 处方表
-- ============================
DROP TABLE IF EXISTS `prescriptions`;
CREATE TABLE `prescriptions` (
  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '处方ID',
  `medical_record_id` INT(11) UNSIGNED NOT NULL COMMENT '病历ID',
  `medicine_id` INT(11) UNSIGNED NOT NULL COMMENT '药品ID',
  `quantity` INT(5) NOT NULL COMMENT '数量',
  `dosage` VARCHAR(100) NULL DEFAULT NULL COMMENT '用法用量',
  `duration` VARCHAR(50) NULL DEFAULT NULL COMMENT '用药时长',
  `notes` TEXT NULL COMMENT '备注',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_medical_record_id`(`medical_record_id`) USING BTREE,
  INDEX `idx_medicine_id`(`medicine_id`) USING BTREE,
  CONSTRAINT `fk_prescription_record` FOREIGN KEY (`medical_record_id`) 
    REFERENCES `medical_records` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_prescription_medicine` FOREIGN KEY (`medicine_id`) 
    REFERENCES `medicines` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '处方记录表';

-- ============================
-- 预约表
-- ============================
DROP TABLE IF EXISTS `appointments`;
CREATE TABLE `appointments` (
  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '预约ID',
  `patient_id` INT(11) UNSIGNED NOT NULL COMMENT '患者ID',
  `doctor_id` INT(11) UNSIGNED NOT NULL COMMENT '医生ID',
  `appointment_date` DATETIME NOT NULL COMMENT '预约日期时间',
  `status` ENUM('已预约','已完成','已取消','爽约') NOT NULL DEFAULT '已预约' COMMENT '预约状态',
  `reason` VARCHAR(200) NULL DEFAULT NULL COMMENT '预约原因',
  `notes` TEXT NULL COMMENT '备注',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_doctor_id`(`doctor_id`) USING BTREE,
  INDEX `idx_appointment_date`(`appointment_date`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  CONSTRAINT `fk_appointment_patient` FOREIGN KEY (`patient_id`)
    REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_appointment_doctor` FOREIGN KEY (`doctor_id`)
    REFERENCES `doctors` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '预约挂号表';

-- ============================
-- 检查项目表
-- ============================
DROP TABLE IF EXISTS `examinations`;
CREATE TABLE `examinations` (
  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '检查ID',
  `medical_record_id` INT(11) UNSIGNED NOT NULL COMMENT '病历ID',
  `exam_type` VARCHAR(100) NOT NULL COMMENT '检查类型',
  `exam_name` VARCHAR(200) NOT NULL COMMENT '检查项目名称',
  `exam_date` DATETIME NOT NULL COMMENT '检查日期',
  `result` TEXT NULL COMMENT '检查结果',
  `normal_range` VARCHAR(200) NULL DEFAULT NULL COMMENT '正常范围',
  `status` ENUM('待检查','已完成','异常') NOT NULL DEFAULT '待检查' COMMENT '检查状态',
  `cost` DECIMAL(10, 2) NULL DEFAULT NULL COMMENT '检查费用',
  `notes` TEXT NULL COMMENT '备注',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_medical_record_id`(`medical_record_id`) USING BTREE,
  INDEX `idx_exam_type`(`exam_type`) USING BTREE,
  INDEX `idx_exam_date`(`exam_date`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  CONSTRAINT `fk_exam_record` FOREIGN KEY (`medical_record_id`)
    REFERENCES `medical_records` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '检查项目表';

-- ============================
-- 费用记录表
-- ============================
DROP TABLE IF EXISTS `billing`;
CREATE TABLE `billing` (
  `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '费用ID',
  `patient_id` INT(11) UNSIGNED NOT NULL COMMENT '患者ID',
  `medical_record_id` INT(11) UNSIGNED NULL DEFAULT NULL COMMENT '病历ID',
  `bill_date` DATETIME NOT NULL COMMENT '账单日期',
  `item_type` ENUM('挂号费','诊疗费','药品费','检查费','治疗费','其他') NOT NULL COMMENT '费用类型',
  `item_name` VARCHAR(200) NOT NULL COMMENT '费用项目',
  `quantity` INT(5) NOT NULL DEFAULT 1 COMMENT '数量',
  `unit_price` DECIMAL(10, 2) NOT NULL COMMENT '单价',
  `total_amount` DECIMAL(10, 2) NOT NULL COMMENT '总金额',
  `payment_status` ENUM('未支付','已支付','部分支付','已退款') NOT NULL DEFAULT '未支付' COMMENT '支付状态',
  `payment_method` VARCHAR(50) NULL DEFAULT NULL COMMENT '支付方式',
  `notes` TEXT NULL COMMENT '备注',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_patient_id`(`patient_id`) USING BTREE,
  INDEX `idx_medical_record_id`(`medical_record_id`) USING BTREE,
  INDEX `idx_bill_date`(`bill_date`) USING BTREE,
  INDEX `idx_item_type`(`item_type`) USING BTREE,
  INDEX `idx_payment_status`(`payment_status`) USING BTREE,
  CONSTRAINT `fk_billing_patient` FOREIGN KEY (`patient_id`)
    REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_billing_record` FOREIGN KEY (`medical_record_id`)
    REFERENCES `medical_records` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB COMMENT = '费用记录表';

SET FOREIGN_KEY_CHECKS = 1;
