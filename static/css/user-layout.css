/* 用户页面通用布局样式 */

body {
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* 顶部用户信息栏 */
.top-user-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.logout-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s;
}

.logout-btn:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    text-decoration: none;
}

/* 侧边导航栏 */
.sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    width: 250px;
    height: calc(100vh - 60px);
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    overflow-y: auto;
    z-index: 999;
}

.sidebar-header {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.sidebar-nav {
    padding: 0;
    margin: 0;
    list-style: none;
}

.sidebar-nav li {
    border-bottom: 1px solid #f0f0f0;
}

.sidebar-nav a {
    display: block;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s;
}

.sidebar-nav a:hover {
    background: #f8f9ff;
    color: #667eea;
    text-decoration: none;
}

.sidebar-nav a.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.sidebar-nav i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* 主内容区域 */
.main-content {
    margin-left: 250px;
    margin-top: 60px;
    padding: 30px;
    min-height: calc(100vh - 60px);
}

.content-wrapper {
    margin-left: 250px;
    margin-top: 60px;
    padding: 30px;
}

/* 通用卡片样式 */
.card-custom {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 30px;
    height: 280px; /* 固定高度 */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 30px;
}

/* 页面标题 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 15px;
    margin-bottom: 30px;
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 30px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 表单控件 */
.form-control {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 统计卡片 */
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stats-number {
    font-size: 32px;
    font-weight: bold;
    color: #667eea;
}

.stats-label {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content,
    .content-wrapper {
        margin-left: 0;
    }
    
    .card-custom {
        height: auto;
        min-height: 200px;
    }
    
    .top-user-bar {
        padding: 10px 15px;
    }
    
    .user-info div {
        display: none;
    }
    
    .logout-btn {
        padding: 4px 10px;
        font-size: 12px;
    }
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
    display: none;
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    margin-right: 10px;
}

@media (max-width: 768px) {
    .mobile-menu-btn {
        display: inline-block;
    }
}

/* 页脚样式 */
.footer-custom {
    background-color: #343a40;
    color: white;
    padding: 20px 0;
    margin-left: 250px;
    margin-top: 40px;
    text-align: center;
}

@media (max-width: 768px) {
    .footer-custom {
        margin-left: 0;
    }
}
