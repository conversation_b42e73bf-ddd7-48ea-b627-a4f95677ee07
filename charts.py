# author:axbros
from pyecharts import options as opts
from pyecharts.charts import Graph
from utils import relations
def get_chart():
    nodes = [
        {"name": "含有疼痛病况", "symbolSize": 10},
    ]
    user_nodes=relations()
    for user_node in user_nodes:
        nodes.append(user_node)
    links = []
    for i in nodes:
        for j in nodes:
            links.append({"source": i.get("name"), "target": j.get("name")})
    c = (
        Graph()
        .add("", nodes, links, repulsion=8000)
        .set_global_opts(title_opts=opts.TitleOpts(title="疾病关联分析"))
        .render("templates/graph_base.html")
    )
    return c
get_chart()


